2025-07-02 13:47:28,761 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:168 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 13:47:28,954 - test_module - ERROR - _log:168 - Error message
2025-07-02 13:47:28,954 - test_module - CRITICAL - _log:168 - Critical message
2025-07-02 13:48:10,685 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:168 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 13:48:10,925 - test_module - ERROR - _log:168 - Error message
2025-07-02 13:48:10,925 - test_module - CRITICAL - _log:168 - Critical message
2025-07-02 14:01:02,318 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 14:01:02,612 - test_module - ERROR - _log:166 - Error message
2025-07-02 14:01:02,612 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:22:59,986 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:23:00,227 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:23:00,228 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:23:13,197 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:23:13,500 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:23:13,500 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:38:30,946 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:30,978 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:31,038 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:31,071 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:07,371 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:07,414 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:35,348 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:52:35,696 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:52:35,697 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:52:36,076 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,129 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,189 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,226 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:03:15,320 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,355 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,450 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,451 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:03:15,452 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:03:15,463 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,464 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:03:15,464 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:03:15,472 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,520 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,521 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:03:16,017 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:03:16,290 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023D457B1648>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:03:16,290 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:03:16,321 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,866 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,931 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,956 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,957 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:06:04,959 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:06:04,979 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,981 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:06:04,982 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:06:05,003 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:05,006 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:06:05,051 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:05,054 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:06:05,078 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:06:05,310 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000016844AEF488>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:06:05,311 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:06:05,345 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:55,864 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:07:56,597 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:07:56,598 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:07:57,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,551 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,652 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,695 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,701 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:07:57,704 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:07:57,724 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,727 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:07:57,729 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:07:57,746 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,748 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:07:57,910 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,914 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:08:01,702 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:08:02,138 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002B7F4836F08>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:08:02,140 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:08:02,194 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:08:02,211 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:08:03,366 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:14:37,917 - augment_raptor.data.processor - ERROR - _log:166 - Error processing data: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-07-02 17:19:53,428 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:19:53,779 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:19:53,779 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:19:54,110 - augment_raptor.data.processor - ERROR - _log:166 - Error processing data: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-07-02 17:19:56,228 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,318 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,385 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,404 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,406 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:19:56,408 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:19:56,425 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,426 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:19:56,427 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:19:56,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,444 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:19:56,529 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,534 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:19:59,854 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:20:00,218 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000210AFC60C48>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:20:00,219 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:20:00,265 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:20:00,282 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:20:01,443 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:27:03,237 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:27:03,767 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:27:03,767 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:27:05,984 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,037 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,089 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,111 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,113 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:27:06,116 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:27:06,135 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,137 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:27:06,138 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:27:06,157 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,160 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:27:06,240 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,244 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:27:09,088 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:27:09,382 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000028110D30DC8>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:27:09,383 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:27:09,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:09,460 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:27:10,714 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
