# Development environment configuration
# This file overrides default.yaml settings for development

# Development-specific app settings
app:
  debug: true
  environment: "development"

# Data settings for development
data:
  # Use smaller cache TTL for development
  cache:
    ttl: 3600  # 1 hour instead of 24 hours
    cleanup_interval: 300  # 5 minutes
  
  # Smaller symbol list for faster testing
  symbols:
    - "VIC"
    - "VHM"
    - "VCB"

# Strategy settings for development/testing
strategy:
  volume_breakout:
    # More sensitive parameters for testing
    volume_threshold: 1.5
    price_threshold: 0.015
    volume_period: 10
    min_volume: 50000

# Backtest settings for development
backtest:
  initial_capital: 50000.0  # Smaller capital for testing
  
  risk:
    risk_per_trade: 0.05    # Higher risk for testing

# Scanner settings for development
scanner:
  lookback_days: 30         # Shorter lookback for faster testing
  min_volume: 50000         # Lower minimum volume

# Development logging - more verbose
logging:
  level: "DEBUG"
  console_level: "DEBUG"
  
  # Enable all logging for development
  log_performance: true
  log_trades: true
  log_signals: true

# Database settings for development
database:
  path: "data/dev_augment_raptor.db"

# Performance settings for development
performance:
  max_workers: 2            # Fewer workers for development
  chunk_size: 10            # Smaller chunks for testing

# Development feature flags
features:
  enable_caching: true
  enable_parallel_processing: false  # Disable for easier debugging
  enable_performance_logging: true
  enable_signal_validation: true
  
  # Development-only features
  enable_debug_output: true
  enable_test_mode: true
