# Augment Raptor API Documentation

## Overview

Augment Raptor cung cấp một API đơn giản và mạnh mẽ để xây dựng các hệ thống giao dịch thuật toán. Tài liệu này mô tả các module chính và cách sử dụng chúng.

## Core Modules

### 1. Data Module (`augment_raptor.data`)

#### VNStockFetcher

Lớp chính để lấy dữ liệu từ VNStock API.

```python
from augment_raptor.data.fetcher import VNStockFetcher

fetcher = VNStockFetcher(base_url="https://api.vnstock.vn", timeout=30)

# Lấy dữ liệu một mã
data = fetcher.fetch("VIC", "2024-01-01", "2024-12-31")

# Lấy dữ liệu nhiều mã
batch_data = fetcher.fetch_batch(["VIC", "VHM", "VCB"], "2024-01-01", "2024-12-31")
```

**Methods:**
- `fetch(symbol: str, start_date: str, end_date: str) -> pd.DataFrame`
- `fetch_batch(symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]`

#### DataProcessor

Xử lý và tính toán các chỉ báo kỹ thuật.

```python
from augment_raptor.data.processor import DataProcessor

processor = DataProcessor()

# Xử lý dữ liệu và thêm chỉ báo kỹ thuật
processed_data = processor.process(raw_data)

# Tính toán tín hiệu Volume Breakout
signals = processor.calculate_volume_breakout_signals(
    data, 
    volume_threshold=2.0, 
    price_threshold=0.02
)
```

**Methods:**
- `process(data: pd.DataFrame) -> pd.DataFrame`
- `calculate_volume_breakout_signals(data: pd.DataFrame, volume_threshold: float, price_threshold: float) -> pd.DataFrame`

#### Storage Classes

Hệ thống cache đa cấp với Memory và DuckDB.

```python
from augment_raptor.data.storage import MemoryCache, DuckDBCache

# Memory cache
memory_cache = MemoryCache()
memory_cache.set("key", data, ttl=3600)
cached_data = memory_cache.get("key")

# DuckDB cache (persistent)
db_cache = DuckDBCache(db_path="data/cache.db")
db_cache.set("key", data)
persistent_data = db_cache.get("key")
```

### 2. Strategies Module (`augment_raptor.strategies`)

#### VolumeBreakoutStrategy

Chiến lược Volume Breakout chính.

```python
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy

# Khởi tạo với tham số mặc định
strategy = VolumeBreakoutStrategy()

# Khởi tạo với tham số tùy chỉnh
custom_params = {
    'volume_threshold': 2.5,
    'price_threshold': 0.03,
    'volume_period': 20,
    'stop_loss_atr': 2.0,
    'take_profit_atr': 4.0
}
strategy = VolumeBreakoutStrategy(parameters=custom_params)

# Tạo tín hiệu giao dịch
signals = strategy.generate_signals(processed_data)

# Phân tích chiến lược
analysis = strategy.analyze(processed_data)
```

**Methods:**
- `generate_signals(data: pd.DataFrame) -> pd.DataFrame`
- `analyze(data: pd.DataFrame) -> Dict[str, Any]`
- `get_parameters() -> Dict[str, Any]`

**Parameters:**
- `volume_threshold`: Ngưỡng khối lượng (default: 2.0)
- `price_threshold`: Ngưỡng thay đổi giá (default: 0.02)
- `volume_period`: Chu kỳ trung bình khối lượng (default: 20)
- `stop_loss_atr`: Stop loss theo ATR (default: 2.0)
- `take_profit_atr`: Take profit theo ATR (default: 4.0)

### 3. Backtest Module (`augment_raptor.backtest`)

#### SimpleBacktest

Engine backtest đơn giản nhưng mạnh mẽ.

```python
from augment_raptor.backtest.simple_backtest import SimpleBacktest

backtest = SimpleBacktest(
    initial_capital=100000,
    commission=0.001,
    slippage=0.0005
)

# Chạy backtest
results = backtest.run(processed_data, strategy)

# Lấy metrics chi tiết
metrics = backtest.get_metrics()
trades = backtest.get_trades()
```

**Methods:**
- `run(data: pd.DataFrame, strategy: Strategy) -> Dict[str, Any]`
- `get_metrics() -> Dict[str, float]`
- `get_trades() -> pd.DataFrame`

**Parameters:**
- `initial_capital`: Vốn ban đầu (default: 100000)
- `commission`: Phí giao dịch (default: 0.001)
- `slippage`: Slippage (default: 0.0005)

#### Metrics

Các metrics được tính toán:

```python
from augment_raptor.backtest.metrics import calculate_metrics

metrics = calculate_metrics(returns, trades)
```

**Available Metrics:**
- `total_return`: Tổng lợi nhuận
- `annualized_return`: Lợi nhuận hàng năm
- `sharpe_ratio`: Tỷ số Sharpe
- `sortino_ratio`: Tỷ số Sortino
- `max_drawdown`: Drawdown tối đa
- `win_rate`: Tỷ lệ thắng
- `profit_factor`: Hệ số lợi nhuận

### 4. CLI Module (`augment_raptor.cli`)

#### Commands

Giao diện dòng lệnh với Click framework.

```python
from augment_raptor.cli.commands import cli

# Sử dụng programmatically
from click.testing import CliRunner

runner = CliRunner()
result = runner.invoke(cli, ['scan', '--symbols', 'VIC'])
```

**Available Commands:**
- `scan`: Quét thị trường
- `backtest`: Chạy backtest
- `update-data`: Cập nhật dữ liệu

### 5. Utils Module (`augment_raptor.utils`)

#### Config

Quản lý cấu hình hệ thống.

```python
from augment_raptor.utils.config import Config

config = Config(config_dir="config", environment="development")

# Lấy giá trị cấu hình
api_url = config.get("data.api.url")
strategy_params = config.get("strategy.volume_breakout")

# Cập nhật cấu hình
config.set("strategy.volume_breakout.volume_threshold", 2.5)
config.update({"new_section": {"key": "value"}})

# Lưu cấu hình
config.save("config/custom.yaml")
```

#### Logger

Hệ thống logging có cấu trúc.

```python
from augment_raptor.utils.logger import Logger

logger = Logger(__name__)

logger.info("Processing data...")
logger.warning("Low volume detected")
logger.error("Failed to fetch data", extra={"symbol": "VIC"})
```

#### Helpers

Các hàm tiện ích.

```python
from augment_raptor.utils.helpers import (
    generate_cache_key,
    validate_date_format,
    calculate_returns,
    format_currency
)

# Tạo cache key
key = generate_cache_key("VIC", "2024-01-01", "2024-12-31")

# Validate date
is_valid = validate_date_format("2024-01-01")

# Tính returns
returns = calculate_returns(prices)

# Format currency
formatted = format_currency(1234567.89)  # "1,234,568 VND"
```

## Usage Examples

### Complete Workflow

```python
from augment_raptor.data.fetcher import VNStockFetcher
from augment_raptor.data.processor import DataProcessor
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
from augment_raptor.backtest.simple_backtest import SimpleBacktest
from augment_raptor.utils.config import Config

# Load configuration
config = Config()

# Initialize components
fetcher = VNStockFetcher()
processor = DataProcessor()
strategy = VolumeBreakoutStrategy(
    parameters=config.get("strategy.volume_breakout")
)
backtest = SimpleBacktest(**config.get("backtest"))

# Fetch and process data
raw_data = fetcher.fetch("VIC", "2024-01-01", "2024-12-31")
processed_data = processor.process(raw_data)

# Generate signals
signals = strategy.generate_signals(processed_data)
print(f"Generated {len(signals)} signals")

# Run backtest
results = backtest.run(processed_data, strategy)
print(f"Total Return: {results['total_return']:.2%}")
print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {results['max_drawdown']:.2%}")
```

### Custom Strategy Development

```python
from augment_raptor.strategies.base import Strategy
import pandas as pd

class CustomStrategy(Strategy):
    def __init__(self, parameters=None):
        super().__init__(parameters)
        self.custom_param = self.parameters.get('custom_param', 1.0)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        # Implement custom logic
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0  # 0: hold, 1: buy, -1: sell
        signals['strength'] = 0.0
        
        # Your custom signal logic here
        
        return signals
    
    def analyze(self, data: pd.DataFrame) -> dict:
        signals = self.generate_signals(data)
        return {
            'total_signals': len(signals[signals['signal'] != 0]),
            'buy_signals': len(signals[signals['signal'] == 1]),
            'sell_signals': len(signals[signals['signal'] == -1])
        }
```

## Error Handling

Tất cả các module đều có error handling tích hợp:

```python
try:
    data = fetcher.fetch("INVALID_SYMBOL", "2024-01-01", "2024-12-31")
    if data.empty:
        print("No data returned")
except Exception as e:
    print(f"Error fetching data: {e}")
```

## Performance Considerations

- Sử dụng caching để tránh fetch dữ liệu trùng lặp
- Batch processing cho nhiều symbols
- Vectorized operations với pandas/numpy
- Memory management cho datasets lớn

## Configuration

Tất cả các components đều có thể cấu hình qua YAML files:

```yaml
data:
  api:
    base_url: "https://api.vnstock.vn"
    timeout: 30
  cache:
    ttl: 3600

strategy:
  volume_breakout:
    volume_threshold: 2.0
    price_threshold: 0.02

backtest:
  initial_capital: 100000
  commission: 0.001
  slippage: 0.0005
```
