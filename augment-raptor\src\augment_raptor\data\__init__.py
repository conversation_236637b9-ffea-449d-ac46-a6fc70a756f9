"""
Data module for Augment Raptor

This module handles data fetching, processing, and storage operations.
"""

from .fetcher import <PERSON><PERSON><PERSON><PERSON>, VNStockFetcher
from .processor import DataProcessor
from .storage import Cache, MemoryCache

# Try to import Duck<PERSON><PERSON><PERSON>, only if duckdb is available
try:
    from .storage import Duck<PERSON><PERSON>ache
    __all__ = [
        "DataFetcher",
        "VNStockFetcher",
        "DataProcessor",
        "Cache",
        "MemoryCache",
        "DuckDBCache"
    ]
except ImportError:
    __all__ = [
        "DataFetcher",
        "VNStockFetcher",
        "DataProcessor",
        "Cache",
        "MemoryCache"
    ]
