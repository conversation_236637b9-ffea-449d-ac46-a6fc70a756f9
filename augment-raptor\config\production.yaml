# Production environment configuration
# This file overrides default.yaml settings for production

# Production app settings
app:
  debug: false
  environment: "production"

# Data settings for production
data:
  # Longer cache TTL for production
  cache:
    ttl: 86400  # 24 hours
    cleanup_interval: 3600  # 1 hour
  
  # Full symbol list for production
  symbols:
    - "VIC"
    - "VHM"
    - "VCB"
    - "BID"
    - "CTG"
    - "HPG"
    - "MSN"
    - "TCB"
    - "VRE"
    - "PLX"
    - "GAS"
    - "SAB"
    - "MWG"
    - "FPT"
    - "VNM"

# Strategy settings for production
strategy:
  volume_breakout:
    # Conservative parameters for production
    volume_threshold: 2.5
    price_threshold: 0.025
    volume_period: 20
    confirmation_period: 3
    stop_loss_atr: 2.0
    take_profit_atr: 3.0
    min_volume: 200000
    trend_filter: true

# Backtest settings for production
backtest:
  initial_capital: 100000.0
  commission: 0.0015        # Slightly higher commission for reality
  slippage: 0.0015         # Slightly higher slippage
  
  risk:
    max_position_size: 0.05  # More conservative position sizing
    risk_per_trade: 0.01     # Lower risk per trade

# Scanner settings for production
scanner:
  lookback_days: 90
  min_volume: 200000        # Higher minimum volume
  min_price: 15000         # Higher minimum price

# Production logging - less verbose
logging:
  level: "INFO"
  console_level: "WARNING"  # Only warnings and errors to console
  
  # Log rotation for production
  max_file_size: "50MB"
  backup_count: 10
  
  # Selective logging for production
  log_performance: true
  log_trades: true
  log_signals: false        # Disable signal logging to reduce noise

# Database settings for production
database:
  path: "data/prod_augment_raptor.db"
  
  connection:
    timeout: 60
    pool_size: 10

# Notification settings for production
notifications:
  enabled: true
  
  email:
    enabled: true
    # Use environment variables for sensitive data
    smtp_server: "${SMTP_SERVER}"
    smtp_port: 587
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
    from_address: "${EMAIL_FROM}"
    to_addresses: 
      - "${EMAIL_TO}"

# Performance settings for production
performance:
  max_workers: 8            # More workers for production
  chunk_size: 500           # Larger chunks for efficiency
  
  memory_limit: "4GB"       # Higher memory limit
  gc_threshold: 0.9

# Security settings for production
security:
  # Use environment variables for API keys
  api_keys:
    vnstock: "${VNSTOCK_API_KEY}"
  
  rate_limiting:
    enabled: true
    requests_per_minute: 30  # More conservative rate limiting

# Production feature flags
features:
  enable_caching: true
  enable_parallel_processing: true
  enable_performance_logging: true
  enable_signal_validation: true
  
  # Disable development features
  enable_debug_output: false
  enable_test_mode: false
