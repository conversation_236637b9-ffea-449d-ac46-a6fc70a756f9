2025-07-02 12:09:31,497 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:40:31,579 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:40:31,581 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 5 rows, need at least 50
2025-07-02 13:40:31,583 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 10 rows, need at least 50
2025-07-02 13:41:26,947 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:42:04,568 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:42:04,632 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:43:03,326 - tests.test_integration - INFO - _log:168 - Test log message
2025-07-02 13:43:03,355 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:43:03,366 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 5 rows, need at least 50
2025-07-02 13:43:03,456 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 10 rows, need at least 50
2025-07-02 13:43:03,485 - tests.test_integration - INFO - _log:168 - Starting end-to-end test
2025-07-02 13:43:59,009 - tests.test_integration - INFO - _log:168 - Test log message
2025-07-02 13:43:59,034 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:43:59,097 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:43:59,126 - tests.test_integration - INFO - _log:168 - Starting end-to-end test
2025-07-02 13:43:59,167 - augment_raptor.data.processor - INFO - _log:168 - Processed 49 records
2025-07-02 13:43:59,167 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 49 rows, need at least 50
2025-07-02 13:44:34,754 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:44:44,724 - tests.test_integration - INFO - _log:168 - Starting end-to-end test
2025-07-02 13:44:44,767 - augment_raptor.data.processor - INFO - _log:168 - Processed 49 records
2025-07-02 13:44:44,768 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 49 rows, need at least 50
2025-07-02 13:45:57,016 - tests.test_integration - INFO - _log:168 - Starting end-to-end test
2025-07-02 13:45:57,055 - augment_raptor.data.processor - INFO - _log:168 - Processed 59 records
2025-07-02 13:45:57,114 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 1 trades, 2.09% return
2025-07-02 13:45:57,114 - tests.test_integration - INFO - _log:168 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 13:46:07,303 - tests.test_integration - INFO - _log:168 - Test log message
2025-07-02 13:46:07,338 - augment_raptor.data.processor - INFO - _log:168 - Processed 5 records
2025-07-02 13:46:07,401 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:46:07,428 - tests.test_integration - INFO - _log:168 - Starting end-to-end test
2025-07-02 13:46:07,462 - augment_raptor.data.processor - INFO - _log:168 - Processed 59 records
2025-07-02 13:46:07,512 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 1 trades, 2.09% return
2025-07-02 13:46:07,513 - tests.test_integration - INFO - _log:168 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 13:47:28,253 - augment_raptor.data.storage - INFO - _log:168 - Memory cache cleared
2025-07-02 13:47:28,401 - augment_raptor.data.fetcher - INFO - _log:168 - Fetched 2 records for VIC
2025-07-02 13:47:28,426 - augment_raptor.data.processor - INFO - _log:168 - Processed 54 records
2025-07-02 13:47:28,455 - augment_raptor.data.processor - INFO - _log:168 - Processed 54 records
2025-07-02 13:47:28,760 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 9 rows, need at least 50
2025-07-02 13:47:28,761 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:168 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 13:47:28,761 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Empty dataframe provided
2025-07-02 13:47:28,841 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:47:28,954 - test_module - INFO - _log:168 - Info message
2025-07-02 13:47:28,954 - test_module - WARNING - _log:168 - Warning message
2025-07-02 13:47:28,954 - test_module - ERROR - _log:168 - Error message
2025-07-02 13:47:28,954 - test_module - CRITICAL - _log:168 - Critical message
2025-07-02 13:47:28,955 - test_module - INFO - _log:168 - Structured message | param1=value1, param2=123
2025-07-02 13:47:28,955 - test_module - INFO - _log:168 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 13:48:10,405 - augment_raptor.data.storage - INFO - _log:168 - Memory cache cleared
2025-07-02 13:48:10,596 - augment_raptor.data.fetcher - INFO - _log:168 - Fetched 2 records for VIC
2025-07-02 13:48:10,618 - augment_raptor.data.processor - INFO - _log:168 - Processed 54 records
2025-07-02 13:48:10,641 - augment_raptor.data.processor - INFO - _log:168 - Processed 54 records
2025-07-02 13:48:10,670 - augment_raptor.data.processor - INFO - _log:168 - Processed 54 records
2025-07-02 13:48:10,684 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Insufficient data: 9 rows, need at least 50
2025-07-02 13:48:10,685 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:168 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 13:48:10,685 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:168 - Empty dataframe provided
2025-07-02 13:48:10,768 - augment_raptor.backtest.simple_backtest - INFO - _log:168 - Backtest completed: 0 trades, 0.00% return
2025-07-02 13:48:10,925 - test_module - INFO - _log:168 - Info message
2025-07-02 13:48:10,925 - test_module - WARNING - _log:168 - Warning message
2025-07-02 13:48:10,925 - test_module - ERROR - _log:168 - Error message
2025-07-02 13:48:10,925 - test_module - CRITICAL - _log:168 - Critical message
2025-07-02 13:48:10,926 - test_module - INFO - _log:168 - Structured message | param1=value1, param2=123
2025-07-02 13:48:10,926 - test_module - INFO - _log:168 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 14:01:01,930 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 14:01:02,194 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 14:01:02,228 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 14:01:02,265 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 14:01:02,302 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 14:01:02,317 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 14:01:02,318 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 14:01:02,319 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 14:01:02,420 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 14:01:02,611 - test_module - INFO - _log:166 - Info message
2025-07-02 14:01:02,612 - test_module - WARNING - _log:166 - Warning message
2025-07-02 14:01:02,612 - test_module - ERROR - _log:166 - Error message
2025-07-02 14:01:02,612 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 14:01:02,612 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 14:01:02,612 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 14:01:02,662 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 14:01:02,698 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 14:01:02,760 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 14:01:02,805 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 14:01:02,846 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 14:01:02,915 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 14:01:02,915 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 14:54:06,868 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 20 rows, need at least 50
2025-07-02 14:56:26,548 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 20 rows, need at least 50
2025-07-02 16:22:59,320 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 16:22:59,883 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 16:22:59,915 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:22:59,944 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:22:59,973 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:22:59,985 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 16:22:59,986 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:22:59,988 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 16:23:00,089 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:23:00,227 - test_module - INFO - _log:166 - Info message
2025-07-02 16:23:00,227 - test_module - WARNING - _log:166 - Warning message
2025-07-02 16:23:00,227 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:23:00,228 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:23:00,228 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 16:23:00,228 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 16:23:00,256 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 16:23:00,284 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 16:23:00,344 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:23:00,372 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 16:23:00,410 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 16:23:00,464 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 16:23:00,465 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 16:23:12,595 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 16:23:13,008 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 16:23:13,054 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:23:13,093 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:23:13,132 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:23:13,196 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 16:23:13,197 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:23:13,198 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 16:23:13,309 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:23:13,498 - test_module - INFO - _log:166 - Info message
2025-07-02 16:23:13,499 - test_module - WARNING - _log:166 - Warning message
2025-07-02 16:23:13,500 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:23:13,500 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:23:13,501 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 16:23:13,501 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 16:23:13,545 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 16:23:13,580 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 16:23:13,641 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:23:13,686 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 16:23:13,729 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 16:23:13,796 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 16:23:13,796 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 16:36:00,025 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 16:38:30,898 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:30,946 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:30,978 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:31,038 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:38:31,071 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:07,335 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:07,371 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:07,414 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:34,597 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 16:52:35,221 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 16:52:35,271 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:52:35,303 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:52:35,337 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 16:52:35,346 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 16:52:35,348 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 16:52:35,350 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 16:52:35,452 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:52:35,696 - test_module - INFO - _log:166 - Info message
2025-07-02 16:52:35,696 - test_module - WARNING - _log:166 - Warning message
2025-07-02 16:52:35,696 - test_module - ERROR - _log:166 - Error message
2025-07-02 16:52:35,697 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 16:52:35,697 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 16:52:35,697 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 16:52:35,753 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 16:52:35,788 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 16:52:35,853 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 16:52:35,904 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 16:52:35,944 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 16:52:36,016 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 16:52:36,016 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 16:52:36,076 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,129 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,189 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 16:52:36,226 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:03:15,251 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,320 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,355 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,450 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,451 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:03:15,452 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:03:15,463 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,464 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:03:15,464 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:03:15,472 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,520 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:03:15,521 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:03:16,017 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:03:16,290 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023D457B1648>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:03:16,290 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:03:16,321 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,775 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,866 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,931 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,956 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,957 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:06:04,959 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:06:04,979 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:04,981 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:06:04,982 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:06:05,003 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:05,006 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:06:05,051 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:06:05,054 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:06:05,078 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:06:05,310 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000016844AEF488>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:06:05,311 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:06:05,345 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:55,047 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:07:55,595 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 17:07:55,663 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:07:55,745 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:07:55,832 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:07:55,862 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 17:07:55,864 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:07:55,865 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 17:07:56,228 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:07:56,597 - test_module - INFO - _log:166 - Info message
2025-07-02 17:07:56,597 - test_module - WARNING - _log:166 - Warning message
2025-07-02 17:07:56,597 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:07:56,598 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:07:56,598 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 17:07:56,598 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 17:07:56,688 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 17:07:56,752 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:07:56,887 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:07:56,984 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 17:07:57,057 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 17:07:57,215 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 17:07:57,216 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 17:07:57,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,551 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,652 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,695 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,701 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:07:57,704 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:07:57,724 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,727 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:07:57,729 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:07:57,746 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,748 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:07:57,910 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:07:57,914 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:08:01,702 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:08:02,138 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002B7F4836F08>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:08:02,140 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:08:02,194 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:08:02,211 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:08:03,366 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:14:37,917 - augment_raptor.data.processor - ERROR - _log:166 - Error processing data: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-07-02 17:14:38,134 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:14:38,206 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:14:38,318 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:14:38,459 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:14:39,976 - augment_raptor.data.processor - WARNING - _log:166 - Empty dataframe provided for processing
2025-07-02 17:14:40,078 - augment_raptor.data.processor - INFO - _log:166 - Processed 1000 records
2025-07-02 17:19:52,992 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:19:53,312 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 17:19:53,348 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:19:53,379 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:19:53,415 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:19:53,427 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 17:19:53,428 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:19:53,429 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 17:19:53,541 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:19:53,778 - test_module - INFO - _log:166 - Info message
2025-07-02 17:19:53,779 - test_module - WARNING - _log:166 - Warning message
2025-07-02 17:19:53,779 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:19:53,779 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:19:53,779 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 17:19:53,779 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 17:19:53,832 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 17:19:53,863 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:19:53,929 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:19:53,985 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 17:19:54,027 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 17:19:54,096 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 17:19:54,096 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 17:19:54,110 - augment_raptor.data.processor - ERROR - _log:166 - Error processing data: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-07-02 17:19:54,217 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:19:54,261 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:19:54,312 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:19:54,407 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:19:55,796 - augment_raptor.data.processor - WARNING - _log:166 - Empty dataframe provided for processing
2025-07-02 17:19:55,841 - augment_raptor.data.processor - INFO - _log:166 - Processed 1000 records
2025-07-02 17:19:56,228 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,318 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,385 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,404 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,406 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:19:56,408 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:19:56,425 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,426 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:19:56,427 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:19:56,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,444 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:19:56,529 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:19:56,534 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:19:59,854 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:20:00,218 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000210AFC60C48>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:20:00,219 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:20:00,265 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:20:00,282 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:20:01,443 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:24:41,264 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:24:41,620 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:24:41,728 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:24:41,788 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:24:41,908 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:24:43,231 - augment_raptor.data.processor - WARNING - _log:166 - Empty dataframe provided for processing
2025-07-02 17:24:43,273 - augment_raptor.data.processor - INFO - _log:166 - Processed 1000 records
2025-07-02 17:26:41,952 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:26:42,001 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:26:42,025 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:26:42,058 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:26:42,125 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:26:43,266 - augment_raptor.data.processor - WARNING - _log:166 - Empty dataframe provided for processing
2025-07-02 17:26:43,352 - augment_raptor.data.processor - INFO - _log:166 - Processed 1000 records
2025-07-02 17:27:02,471 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:27:03,019 - augment_raptor.data.fetcher - INFO - _log:166 - Fetched 2 records for VIC
2025-07-02 17:27:03,075 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:27:03,167 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:27:03,216 - augment_raptor.data.processor - INFO - _log:166 - Processed 54 records
2025-07-02 17:27:03,236 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Insufficient data: 9 rows, need at least 50
2025-07-02 17:27:03,237 - augment_raptor.strategies.base.Volume Breakout - ERROR - _log:166 - Missing required columns: ['Open', 'High', 'Low', 'Volume']
2025-07-02 17:27:03,237 - augment_raptor.strategies.base.Volume Breakout - WARNING - _log:166 - Empty dataframe provided
2025-07-02 17:27:03,435 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:27:03,767 - test_module - INFO - _log:166 - Info message
2025-07-02 17:27:03,767 - test_module - WARNING - _log:166 - Warning message
2025-07-02 17:27:03,767 - test_module - ERROR - _log:166 - Error message
2025-07-02 17:27:03,767 - test_module - CRITICAL - _log:166 - Critical message
2025-07-02 17:27:03,768 - test_module - INFO - _log:166 - Structured message | param1=value1, param2=123
2025-07-02 17:27:03,768 - test_module - INFO - _log:166 - Performance: test_operation completed | operation=test_operation, duration_seconds=1.5, extra_param=value
2025-07-02 17:27:03,851 - tests.test_integration - INFO - _log:166 - Test log message
2025-07-02 17:27:03,929 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:27:04,023 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 0 trades, 0.00% return
2025-07-02 17:27:04,095 - tests.test_integration - INFO - _log:166 - Starting end-to-end test
2025-07-02 17:27:04,141 - augment_raptor.data.processor - INFO - _log:166 - Processed 59 records
2025-07-02 17:27:04,235 - augment_raptor.backtest.simple_backtest - INFO - _log:166 - Backtest completed: 1 trades, 2.09% return
2025-07-02 17:27:04,235 - tests.test_integration - INFO - _log:166 - End-to-end test completed | total_signals=1, final_capital=51042.91172367153, total_return=0.020858234473430638
2025-07-02 17:27:04,285 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:27:04,366 - augment_raptor.data.processor - INFO - _log:166 - Processed 5 records
2025-07-02 17:27:04,474 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:27:04,521 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:27:04,618 - augment_raptor.data.processor - INFO - _log:166 - Processed 100 records
2025-07-02 17:27:05,761 - augment_raptor.data.processor - WARNING - _log:166 - Empty dataframe provided for processing
2025-07-02 17:27:05,823 - augment_raptor.data.processor - INFO - _log:166 - Processed 1000 records
2025-07-02 17:27:05,984 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,037 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,089 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,111 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,113 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from 2023-01-01 to 2023-01-31
2025-07-02 17:27:06,116 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:27:06,135 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,137 - augment_raptor.cli.commands - INFO - _log:166 - Running backtest for VIC from 2023-01-01 to 2023-01-31
2025-07-02 17:27:06,138 - augment_raptor.cli.commands - ERROR - _log:166 - Backtest failed: No data available for VIC
2025-07-02 17:27:06,157 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,160 - augment_raptor.cli.commands - INFO - _log:166 - Updating data for 1 symbols
2025-07-02 17:27:06,240 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:06,244 - augment_raptor.cli.commands - INFO - _log:166 - Scanning 1 symbols from invalid-date to 2025-07-02
2025-07-02 17:27:09,088 - urllib3.connectionpool - DEBUG - _new_conn:975 - Starting new HTTPS connection (1): api.vnstock.vn:443
2025-07-02 17:27:09,382 - augment_raptor.data.fetcher - ERROR - _log:166 - Error fetching data for VIC: HTTPSConnectionPool(host='api.vnstock.vn', port=443): Max retries exceeded with url: /stock/VIC/historical?start_date=invalid-date&end_date=2025-07-02&resolution=1D (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000028110D30DC8>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))
2025-07-02 17:27:09,383 - augment_raptor.cli.commands - WARNING - _log:166 - No data for VIC
2025-07-02 17:27:09,443 - augment_raptor.cli.commands - INFO - _log:166 - Augment Raptor CLI started - Environment: development
2025-07-02 17:27:09,460 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:27:10,714 - augment_raptor.data.storage - INFO - _log:166 - Memory cache cleared
2025-07-02 17:31:54,262 - __main__ - INFO - _log:166 - Starting Augment Raptor
