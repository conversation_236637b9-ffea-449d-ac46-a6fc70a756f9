"""
Integration tests for Augment Raptor

This module contains integration tests to validate that all
components work together correctly.
"""

import pytest
import pandas as pd
import sys
from pathlib import Path

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from augment_raptor.utils.config import Config
from augment_raptor.utils.logger import Logger
from augment_raptor.data.processor import DataProcessor
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
from augment_raptor.backtest.simple_backtest import SimpleBacktest


class TestIntegration:
    """Integration tests for the complete system."""

    def test_config_loading(self):
        """Test configuration loading works."""
        config = Config(config_dir="config", environment="development")

        # Test basic config access
        assert config.get("app.name") == "Augment Raptor"
        assert config.get("app.version") == "0.1.0"

        # Test strategy config
        strategy_config = config.get_strategy_config()
        assert "volume_breakout" in strategy_config
        assert strategy_config["volume_breakout"]["volume_threshold"] == 1.5  # dev value

    def test_logger_initialization(self):
        """Test logger initialization works."""
        logger = Logger(__name__)

        # Test logging methods exist
        assert hasattr(logger, "info")
        assert hasattr(logger, "error")
        assert hasattr(logger, "debug")
        assert hasattr(logger, "warning")

        # Test logging doesn't crash
        logger.info("Test log message")
        logger.debug("Debug message", test_param="value")

    def test_data_processor(self):
        """Test data processor with sample data."""
        processor = DataProcessor()

        # Create sample OHLCV data
        sample_data = pd.DataFrame(
            {
                "Open": [100, 102, 101, 103, 105],
                "High": [105, 106, 104, 107, 108],
                "Low": [98, 100, 99, 101, 103],
                "Close": [102, 101, 103, 105, 107],
                "Volume": [1000000, 1200000, 800000, 1500000, 2000000],
            }
        )

        # Process data
        processed_data = processor.process(sample_data)

        # Verify indicators are calculated
        assert "Price_Change_Pct" in processed_data.columns
        assert "Volume_SMA_20" in processed_data.columns
        assert "ATR_14" in processed_data.columns
        assert len(processed_data) == len(sample_data)

    def test_volume_breakout_strategy(self):
        """Test Volume Breakout strategy."""
        strategy = VolumeBreakoutStrategy()

        # Test strategy properties
        assert strategy.name == "Volume Breakout"
        assert strategy.get_parameters()["volume_threshold"] == 2.0

        # Create sample data with volume breakout - enough rows for validation
        import numpy as np

        np.random.seed(42)
        n_rows = 60  # More than minimum required (50)

        # Generate realistic price data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        sample_data = pd.DataFrame(
            {
                "Open": prices[:-1],
                "High": [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
                "Low": [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
                "Close": prices[1:],
                "Volume": [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)],
            }
        )

        # Test data validation
        assert strategy.validate_data(sample_data) == True

        # Test signal generation
        signals = strategy.generate_signals(sample_data)
        assert "signal" in signals.columns
        assert "signal_strength" in signals.columns

    def test_backtest_engine(self):
        """Test backtest engine with simple data."""
        backtest = SimpleBacktest(initial_capital=100000)
        strategy = VolumeBreakoutStrategy()

        # Create sample data with enough rows for strategy validation
        import numpy as np

        np.random.seed(42)
        n_rows = 60
        dates = pd.date_range("2024-01-01", periods=n_rows, freq="D")

        # Generate realistic data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        sample_data = pd.DataFrame(
            {
                "Open": prices[:-1],
                "High": [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
                "Low": [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
                "Close": prices[1:],
                "Volume": [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)],
            },
            index=dates[:-1],
        )  # Use n_rows-1 dates to match data length

        # Run backtest
        results = backtest.run(strategy, sample_data)

        # Verify results structure
        assert "strategy" in results
        assert "total_return" in results
        assert "final_capital" in results
        assert "metrics" in results
        assert results["initial_capital"] == 100000

    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        # 1. Load configuration
        config = Config(config_dir="config", environment="development")

        # 2. Initialize logger
        logger = Logger(__name__)
        logger.info("Starting end-to-end test")

        # 3. Initialize components
        processor = DataProcessor()
        strategy_params = config.get("strategy.volume_breakout", {})
        strategy = VolumeBreakoutStrategy(strategy_params)
        backtest = SimpleBacktest(initial_capital=50000)  # Dev capital

        # 4. Create realistic sample data
        dates = pd.date_range("2024-01-01", periods=60, freq="D")  # Increased to 60
        import numpy as np

        # Generate more realistic price data
        np.random.seed(42)  # For reproducible tests
        prices = [100]
        volumes = []

        for i in range(59):  # Changed to 59 to get 60 total prices
            # Random walk for prices
            change = np.random.normal(0, 0.02)  # 2% daily volatility
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))  # Minimum price

            # Volume with occasional spikes
            base_volume = 1000000
            if np.random.random() < 0.1:  # 10% chance of volume spike
                volume = base_volume * np.random.uniform(2.5, 4.0)
            else:
                volume = base_volume * np.random.uniform(0.5, 1.5)
            volumes.append(volume)

        # Create OHLC from close prices
        sample_data = pd.DataFrame(
            {"Close": prices[1:], "Volume": volumes}, index=dates[:-1]  # Skip first price
        )  # Use dates[:-1] to match data length

        # Add OHLC data
        sample_data["Open"] = sample_data["Close"].shift(1).fillna(sample_data["Close"].iloc[0])
        sample_data["High"] = sample_data[["Open", "Close"]].max(axis=1) * np.random.uniform(
            1.0, 1.02, len(sample_data)
        )
        sample_data["Low"] = sample_data[["Open", "Close"]].min(axis=1) * np.random.uniform(
            0.98, 1.0, len(sample_data)
        )

        # 5. Process data
        processed_data = processor.process(sample_data)
        assert not processed_data.empty

        # 6. Analyze with strategy
        analysis = strategy.analyze(processed_data)
        assert "total_signals" in analysis
        assert "strategy" in analysis

        # 7. Run backtest
        backtest_results = backtest.run(strategy, processed_data)
        assert "error" not in backtest_results
        assert backtest_results["initial_capital"] == 50000

        # 8. Log results
        logger.info(
            "End-to-end test completed",
            total_signals=analysis["total_signals"],
            final_capital=backtest_results["final_capital"],
            total_return=backtest_results["total_return"],
        )

        # Test passed if we reach here without exceptions
        assert True


def test_module_imports():
    """Test that all modules can be imported without errors."""
    # Test data modules
    from augment_raptor.data import DataFetcher, DataProcessor, Cache
    from augment_raptor.data.fetcher import VNStockFetcher
    from augment_raptor.data.storage import MemoryCache, DuckDBCache

    # Test strategy modules
    from augment_raptor.strategies import Strategy, VolumeBreakoutStrategy

    # Test backtest modules
    from augment_raptor.backtest import SimpleBacktest, PerformanceMetrics

    # Test utils modules
    from augment_raptor.utils import Config, Logger

    # Test CLI modules
    from augment_raptor.cli import cli

    # All imports successful
    assert True


if __name__ == "__main__":
    # Run basic tests
    test_module_imports()

    # Run integration tests
    test_integration = TestIntegration()
    test_integration.test_config_loading()
    test_integration.test_logger_initialization()
    test_integration.test_data_processor()
    test_integration.test_volume_breakout_strategy()
    test_integration.test_backtest_engine()
    test_integration.test_end_to_end_workflow()

    print("✅ All integration tests passed!")
