"""
Data processor module for cleaning and transforming market data.

This module handles data cleaning, validation, and calculation of
technical indicators and derived metrics.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from ..utils.logger import Logger


class DataProcessor:
    """Data processor for cleaning and transforming market data."""
    
    def __init__(self):
        self.logger = Logger(__name__)
    
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process raw market data."""
        if data.empty:
            self.logger.warning("Empty dataframe provided for processing")
            return data
            
        try:
            # Clean data
            data = self._clean_data(data)
            
            # Calculate basic indicators
            data = self._calculate_basic_indicators(data)
            
            # Calculate volume indicators
            data = self._calculate_volume_indicators(data)
            
            self.logger.info(f"Processed {len(data)} records")
            return data
            
        except Exception as e:
            self.logger.error(f"Error processing data: {str(e)}")
            return pd.DataFrame()
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate market data."""
        # Remove rows with missing OHLCV data
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        data = data.dropna(subset=required_columns)
        
        # Ensure positive values
        for col in required_columns:
            data = data[data[col] > 0]
        
        # Ensure High >= Low, High >= Open, High >= Close
        data = data[data['High'] >= data['Low']]
        data = data[data['High'] >= data['Open']]
        data = data[data['High'] >= data['Close']]
        
        # Sort by date
        if isinstance(data.index, pd.DatetimeIndex):
            data = data.sort_index()
        
        return data
    
    def _calculate_basic_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate basic technical indicators."""
        # Price change
        data['Price_Change'] = data['Close'].diff()
        data['Price_Change_Pct'] = data['Close'].pct_change() * 100
        
        # True Range
        data['True_Range'] = np.maximum(
            data['High'] - data['Low'],
            np.maximum(
                abs(data['High'] - data['Close'].shift(1)),
                abs(data['Low'] - data['Close'].shift(1))
            )
        )
        
        # Average True Range (14 periods)
        data['ATR_14'] = data['True_Range'].rolling(window=14).mean()
        
        # Simple Moving Averages
        for period in [5, 10, 20, 50]:
            data[f'SMA_{period}'] = data['Close'].rolling(window=period).mean()
        
        return data
    
    def _calculate_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate volume-based indicators."""
        # Volume Moving Averages
        for period in [5, 10, 20, 50]:
            data[f'Volume_SMA_{period}'] = data['Volume'].rolling(window=period).mean()
        
        # Volume Ratio (current volume vs average)
        data['Volume_Ratio_20'] = data['Volume'] / data['Volume_SMA_20']
        
        # Volume Price Trend (VPT)
        data['VPT'] = (data['Volume'] * data['Price_Change_Pct']).cumsum()
        
        # On Balance Volume (OBV)
        data['OBV'] = (data['Volume'] * np.sign(data['Price_Change'])).cumsum()
        
        # Money Flow Index components
        data['Typical_Price'] = (data['High'] + data['Low'] + data['Close']) / 3
        data['Money_Flow'] = data['Typical_Price'] * data['Volume']
        
        # Positive and Negative Money Flow
        data['Positive_MF'] = np.where(
            data['Typical_Price'] > data['Typical_Price'].shift(1),
            data['Money_Flow'], 0
        )
        data['Negative_MF'] = np.where(
            data['Typical_Price'] < data['Typical_Price'].shift(1),
            data['Money_Flow'], 0
        )
        
        # Money Flow Index (14 periods)
        positive_mf_sum = data['Positive_MF'].rolling(window=14).sum()
        negative_mf_sum = data['Negative_MF'].rolling(window=14).sum()
        
        data['MFI_14'] = 100 - (100 / (1 + positive_mf_sum / negative_mf_sum))
        
        return data
    
    def calculate_volume_breakout_signals(self, data: pd.DataFrame, 
                                        volume_threshold: float = 2.0,
                                        price_threshold: float = 0.02) -> pd.DataFrame:
        """Calculate Volume Breakout signals."""
        if 'Volume_Ratio_20' not in data.columns:
            data = self._calculate_volume_indicators(data)
        
        # Volume breakout condition
        volume_breakout = data['Volume_Ratio_20'] >= volume_threshold
        
        # Price breakout condition (price increase)
        price_breakout = data['Price_Change_Pct'] >= (price_threshold * 100)
        
        # Combined signal
        data['Volume_Breakout_Signal'] = volume_breakout & price_breakout
        
        # Signal strength (0-1)
        data['Signal_Strength'] = np.minimum(
            data['Volume_Ratio_20'] / volume_threshold,
            data['Price_Change_Pct'] / (price_threshold * 100)
        )
        
        return data
