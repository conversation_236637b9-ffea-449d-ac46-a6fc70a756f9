[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "augment-raptor"
dynamic = ["version"]
description = "Algorithmic Trading System with Volume Breakout Strategy"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Augment Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Augment Team", email = "<EMAIL>"}
]
keywords = ["trading", "algorithmic", "finance", "stock", "vietnam", "volume", "breakout"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Office/Business :: Financial :: Investment",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent",
]
requires-python = ">=3.9"
dependencies = [
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "duckdb>=0.8.0",
    "requests>=2.28.0",
    "PyYAML>=6.0",
    "click>=8.0.0",
    "matplotlib>=3.5.0",
    "plotly>=5.0.0",
    "python-dateutil>=2.8.0",
    "structlog>=22.0.0",
    "numba>=0.56.0",
    "pytest>=7.0.0",
    "pydantic>=1.10.0",
    "tqdm>=4.64.0",
    "memory-profiler>=0.60.0",
    "diskcache>=5.4.0",
    "scipy>=1.9.0",
    "vnstock>=0.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.8.0",
    "pytest-asyncio>=0.21.0",
    "pytest-benchmark>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "isort>=5.10.0",
    "mypy>=0.991",
    "pre-commit>=2.20.0",
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
    "ipython>=8.0.0",
    "jupyter>=1.0.0",
    "notebook>=6.4.0",
    "line-profiler>=4.0.0",
    "py-spy>=0.3.0",
    "bandit>=1.7.0",
    "safety>=2.0.0",
    "types-PyYAML>=6.0.0",
    "types-requests>=2.28.0",
    "coverage>=6.0.0",
    "build>=0.8.0",
    "twine>=4.0.0",
    "python-dotenv>=0.20.0",
    "seaborn>=0.11.0",
    "bokeh>=2.4.0",
    "rich>=12.0.0",
]

[project.urls]
Homepage = "https://github.com/augment/augment-raptor"
Documentation = "https://augment-raptor.readthedocs.io/"
Repository = "https://github.com/augment/augment-raptor.git"
"Bug Tracker" = "https://github.com/augment/augment-raptor/issues"

[project.scripts]
augment-raptor = "augment_raptor.cli.commands:cli"
raptor = "augment_raptor.cli.commands:cli"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
augment_raptor = ["config/*.yaml", "data/*.sql"]

[tool.setuptools_scm]
write_to = "src/augment_raptor/_version.py"

# Black configuration
[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "vnstock.*",
    "duckdb.*",
    "plotly.*",
    "matplotlib.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
