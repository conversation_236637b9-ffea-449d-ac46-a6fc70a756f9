"""
Tests for utils.helpers module.
"""

import pytest
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from unittest.mock import Mock

from augment_raptor.utils.helpers import (
    generate_cache_key,
    validate_date_format,
    get_business_days,
    calculate_returns,
    calculate_volatility,
    normalize_symbol,
    format_currency,
    format_percentage,
    safe_divide,
    chunk_list,
    flatten_dict,
    unflatten_dict,
    merge_dicts,
    get_memory_usage,
    timing_decorator
)


class TestCacheHelpers:
    """Test cache-related helper functions."""
    
    def test_generate_cache_key_basic(self):
        """Test basic cache key generation."""
        key1 = generate_cache_key("test", "data")
        key2 = generate_cache_key("test", "data")
        key3 = generate_cache_key("different", "data")
        
        assert key1 == key2  # Same inputs should generate same key
        assert key1 != key3  # Different inputs should generate different keys
        assert len(key1) == 32  # MD5 hash length
        
    def test_generate_cache_key_with_kwargs(self):
        """Test cache key generation with keyword arguments."""
        key1 = generate_cache_key("test", symbol="VIC", period="1d")
        key2 = generate_cache_key("test", period="1d", symbol="VIC")  # Different order
        key3 = generate_cache_key("test", symbol="VIC", period="1h")
        
        assert key1 == key2  # Order shouldn't matter for kwargs
        assert key1 != key3  # Different values should generate different keys
        
    def test_generate_cache_key_with_complex_data(self):
        """Test cache key generation with complex data types."""
        data = {"nested": {"key": "value"}, "list": [1, 2, 3]}
        key1 = generate_cache_key(data)
        key2 = generate_cache_key(data)
        
        assert key1 == key2
        assert isinstance(key1, str)


class TestDateHelpers:
    """Test date-related helper functions."""
    
    def test_validate_date_format_valid(self):
        """Test date format validation with valid dates."""
        assert validate_date_format("2023-01-01") is True
        assert validate_date_format("2023-12-31") is True
        assert validate_date_format("2023-02-28") is True
        
    def test_validate_date_format_invalid(self):
        """Test date format validation with invalid dates."""
        assert validate_date_format("2023-13-01") is False  # Invalid month
        assert validate_date_format("2023-01-32") is False  # Invalid day
        assert validate_date_format("invalid-date") is False  # Invalid format
        assert validate_date_format("01-01-2023") is False  # Wrong format
        
    def test_validate_date_format_custom_format(self):
        """Test date format validation with custom format."""
        assert validate_date_format("01/01/2023", "%m/%d/%Y") is True
        assert validate_date_format("2023-01-01", "%m/%d/%Y") is False
        
    def test_get_business_days(self):
        """Test business days calculation."""
        # Test a week that includes weekend
        start = "2023-01-02"  # Monday
        end = "2023-01-08"    # Sunday
        
        business_days = get_business_days(start, end)
        
        assert len(business_days) == 5  # Monday to Friday
        assert "2023-01-02" in business_days  # Monday
        assert "2023-01-06" in business_days  # Friday
        assert "2023-01-07" not in business_days  # Saturday
        assert "2023-01-08" not in business_days  # Sunday


class TestCalculationHelpers:
    """Test calculation helper functions."""

    def test_safe_divide(self):
        """Test safe division function."""
        assert safe_divide(10, 2) == 5.0
        assert safe_divide(10, 0) == 0.0  # Should return 0 for division by zero
        assert safe_divide(10, 0, default=np.nan) != safe_divide(10, 0, default=np.nan)  # NaN != NaN

    def test_calculate_volatility(self):
        """Test volatility calculation."""
        # Create sample returns data
        returns = pd.Series([0.02, -0.01, 0.03, -0.02, 0.04, -0.01])

        volatility = calculate_volatility(returns)

        assert isinstance(volatility, pd.Series)
        assert len(volatility) > 0

    def test_calculate_returns(self):
        """Test returns calculation."""
        prices = pd.Series([100, 105, 102, 108])

        returns = calculate_returns(prices)

        assert len(returns) == len(prices)  # Same length but first value is NaN
        assert pd.isna(returns.iloc[0])  # First return should be NaN
        assert abs(returns.iloc[1] - 0.05) < 0.001  # Second return should be ~5%

    def test_calculate_returns_log(self):
        """Test log returns calculation."""
        prices = pd.Series([100, 105, 102, 108])

        returns = calculate_returns(prices, method="log")

        assert len(returns) == len(prices)  # Same length but first value is NaN
        assert pd.isna(returns.iloc[0])  # First return should be NaN
        assert isinstance(returns, pd.Series)


class TestFormatHelpers:
    """Test formatting helper functions."""
    
    def test_format_currency(self):
        """Test currency formatting."""
        assert format_currency(1234.56) == "1,235 VND"  # Default is VND, rounds to nearest
        assert format_currency(1000000) == "1,000,000 VND"
        assert format_currency(1234.56, "USD") == "1,234.56 USD"
        
    def test_format_percentage(self):
        """Test percentage formatting."""
        assert format_percentage(0.1234) == "12.34%"
        assert format_percentage(1.0) == "100.00%"
        assert format_percentage(-0.05) == "-5.00%"
        
    def test_format_percentage_custom_decimals(self):
        """Test percentage formatting with custom decimal places."""
        assert format_percentage(0.1234, decimals=1) == "12.3%"
        assert format_percentage(0.1234, decimals=0) == "12%"


class TestSymbolHelpers:
    """Test symbol-related helper functions."""

    def test_normalize_symbol(self):
        """Test symbol normalization."""
        assert normalize_symbol("vic") == "VIC"
        assert normalize_symbol("VIC") == "VIC"
        assert normalize_symbol("  vic  ") == "VIC"


class TestDataHelpers:
    """Test data processing helper functions."""

    def test_chunk_list(self):
        """Test list chunking."""
        test_list = list(range(10))
        chunks = chunk_list(test_list, 3)

        assert len(chunks) == 4  # [0,1,2], [3,4,5], [6,7,8], [9]
        assert chunks[0] == [0, 1, 2]
        assert chunks[1] == [3, 4, 5]
        assert chunks[3] == [9]

    def test_flatten_dict(self):
        """Test dictionary flattening."""
        nested_dict = {
            'a': {
                'b': {
                    'c': 1
                },
                'd': 2
            },
            'e': 3
        }

        flattened = flatten_dict(nested_dict)

        assert flattened['a.b.c'] == 1
        assert flattened['a.d'] == 2
        assert flattened['e'] == 3

    def test_unflatten_dict(self):
        """Test dictionary unflattening."""
        flat_dict = {
            'a.b.c': 1,
            'a.d': 2,
            'e': 3
        }

        unflattened = unflatten_dict(flat_dict)

        assert unflattened['a']['b']['c'] == 1
        assert unflattened['a']['d'] == 2
        assert unflattened['e'] == 3

    def test_merge_dicts(self):
        """Test dictionary merging."""
        dict1 = {'a': 1, 'b': 2}
        dict2 = {'b': 3, 'c': 4}
        dict3 = {'c': 5, 'd': 6}

        merged = merge_dicts(dict1, dict2, dict3)

        assert merged['a'] == 1
        assert merged['b'] == 3  # Later dict overwrites
        assert merged['c'] == 5  # Later dict overwrites
        assert merged['d'] == 6


class TestUtilityHelpers:
    """Test utility helper functions."""

    def test_get_memory_usage(self):
        """Test memory usage calculation."""
        test_obj = [1, 2, 3, 4, 5]

        memory_str = get_memory_usage(test_obj)

        assert isinstance(memory_str, str)
        assert any(unit in memory_str for unit in ['B', 'KB', 'MB', 'GB'])

    def test_timing_decorator(self):
        """Test timing decorator."""
        @timing_decorator
        def test_function():
            time.sleep(0.01)  # Small delay
            return "result"

        result = test_function()

        assert result == "result"
        # The decorator should work without errors
