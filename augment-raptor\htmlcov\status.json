{"format": 2, "version": "7.2.7", "globals": "22c3f6dabd1228e53c873bdd634bfe4b", "files": {"d_66e1908eb64c8e84___init___py": {"hash": "ceb3da16995ae212f182c5b772303765", "index": {"nums": [0, 1, 8, 0, 0, 0, 0, 0], "html_filename": "d_66e1908eb64c8e84___init___py.html", "relative_filename": "src\\augment_raptor\\__init__.py"}}, "d_123cd40925cbbb75___init___py": {"hash": "8d8a8b50414e7f95e7f74f77dc6ec2f0", "index": {"nums": [0, 1, 3, 0, 0, 0, 0, 0], "html_filename": "d_123cd40925cbbb75___init___py.html", "relative_filename": "src\\augment_raptor\\backtest\\__init__.py"}}, "d_123cd40925cbbb75_metrics_py": {"hash": "cc49ed5260318409a1d02959851fee34", "index": {"nums": [0, 1, 139, 0, 19, 0, 0, 0], "html_filename": "d_123cd40925cbbb75_metrics_py.html", "relative_filename": "src\\augment_raptor\\backtest\\metrics.py"}}, "d_123cd40925cbbb75_simple_backtest_py": {"hash": "fb86ecdeb717fede997688a9edf20390", "index": {"nums": [0, 1, 126, 0, 24, 0, 0, 0], "html_filename": "d_123cd40925cbbb75_simple_backtest_py.html", "relative_filename": "src\\augment_raptor\\backtest\\simple_backtest.py"}}, "d_ffaebc813917a072___init___py": {"hash": "7a44374db924c483d8cd033eea6fdb3d", "index": {"nums": [0, 1, 2, 0, 0, 0, 0, 0], "html_filename": "d_ffaebc813917a072___init___py.html", "relative_filename": "src\\augment_raptor\\cli\\__init__.py"}}, "d_ffaebc813917a072_commands_py": {"hash": "d70a22fbb6dc91d9e3c5946235606949", "index": {"nums": [0, 1, 185, 2, 81, 0, 0, 0], "html_filename": "d_ffaebc813917a072_commands_py.html", "relative_filename": "src\\augment_raptor\\cli\\commands.py"}}, "d_79c74c2446df8b69___init___py": {"hash": "6df232fa1a7020732ea54cff93d93167", "index": {"nums": [0, 1, 8, 0, 2, 0, 0, 0], "html_filename": "d_79c74c2446df8b69___init___py.html", "relative_filename": "src\\augment_raptor\\data\\__init__.py"}}, "d_79c74c2446df8b69_fetcher_py": {"hash": "96c67b27d5649897c14fed28a040925e", "index": {"nums": [0, 1, 63, 8, 9, 0, 0, 0], "html_filename": "d_79c74c2446df8b69_fetcher_py.html", "relative_filename": "src\\augment_raptor\\data\\fetcher.py"}}, "d_79c74c2446df8b69_processor_py": {"hash": "030975a8faba977b0cac1112f999b3e3", "index": {"nums": [0, 1, 61, 0, 1, 0, 0, 0], "html_filename": "d_79c74c2446df8b69_processor_py.html", "relative_filename": "src\\augment_raptor\\data\\processor.py"}}, "d_79c74c2446df8b69_storage_py": {"hash": "64c10bf7f93d895b9216442c95a3e9a9", "index": {"nums": [0, 1, 107, 16, 61, 0, 0, 0], "html_filename": "d_79c74c2446df8b69_storage_py.html", "relative_filename": "src\\augment_raptor\\data\\storage.py"}}, "d_8551d8f34bd0b620___init___py": {"hash": "f0edcd145b9c2bccd7b94185a5df0600", "index": {"nums": [0, 1, 3, 0, 0, 0, 0, 0], "html_filename": "d_8551d8f34bd0b620___init___py.html", "relative_filename": "src\\augment_raptor\\strategies\\__init__.py"}}, "d_8551d8f34bd0b620_base_py": {"hash": "1940c35a734340434cc8e198470f72b8", "index": {"nums": [0, 1, 41, 16, 14, 0, 0, 0], "html_filename": "d_8551d8f34bd0b620_base_py.html", "relative_filename": "src\\augment_raptor\\strategies\\base.py"}}, "d_8551d8f34bd0b620_volume_breakout_py": {"hash": "dd5f26f31d40757234dbcbad3a3e81d7", "index": {"nums": [0, 1, 87, 0, 13, 0, 0, 0], "html_filename": "d_8551d8f34bd0b620_volume_breakout_py.html", "relative_filename": "src\\augment_raptor\\strategies\\volume_breakout.py"}}, "d_f345f19362c68e46___init___py": {"hash": "f155414625402b793f688d134ed80f6c", "index": {"nums": [0, 1, 4, 0, 0, 0, 0, 0], "html_filename": "d_f345f19362c68e46___init___py.html", "relative_filename": "src\\augment_raptor\\utils\\__init__.py"}}, "d_f345f19362c68e46_config_py": {"hash": "ec48a32965daf88dad30629956cb2ca0", "index": {"nums": [0, 1, 102, 0, 12, 0, 0, 0], "html_filename": "d_f345f19362c68e46_config_py.html", "relative_filename": "src\\augment_raptor\\utils\\config.py"}}, "d_f345f19362c68e46_helpers_py": {"hash": "153c1482e91d0b8961e889980b7fb70b", "index": {"nums": [0, 1, 93, 0, 6, 0, 0, 0], "html_filename": "d_f345f19362c68e46_helpers_py.html", "relative_filename": "src\\augment_raptor\\utils\\helpers.py"}}, "d_f345f19362c68e46_logger_py": {"hash": "ddabe5785001f531ddb66fc7b5a49a5e", "index": {"nums": [0, 1, 97, 0, 12, 0, 0, 0], "html_filename": "d_f345f19362c68e46_logger_py.html", "relative_filename": "src\\augment_raptor\\utils\\logger.py"}}, "d_ffaebc813917a072_commands_old_py": {"hash": "f1105ddb9e6d6617b9cdbae5a35a1b1c", "index": {"nums": [0, 1, 185, 2, 185, 0, 0, 0], "html_filename": "d_ffaebc813917a072_commands_old_py.html", "relative_filename": "src\\augment_raptor\\cli\\commands_old.py"}}, "d_8551d8f34bd0b620_volume_breakout_old_py": {"hash": "8c21f6300c2cc28f42bb9c1aded13d19", "index": {"nums": [0, 1, 87, 0, 87, 0, 0, 0], "html_filename": "d_8551d8f34bd0b620_volume_breakout_old_py.html", "relative_filename": "src\\augment_raptor\\strategies\\volume_breakout_old.py"}}}}