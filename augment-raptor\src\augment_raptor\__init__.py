"""
Augment Raptor - Algorithmic Trading System

A modular algorithmic trading system with Volume Breakout strategy.
"""

__version__ = "0.1.0"
__author__ = "Augment Team"
__description__ = "Algorithmic Trading System with Volume Breakout Strategy"

# Package imports
from .data import DataFetcher, DataProcessor
from .strategies import Strategy, VolumeBreakoutStrategy
from .backtest import SimpleBacktest
from .utils import Logger, Config

__all__ = [
    "DataFetcher",
    "DataProcessor",
    "Strategy",
    "VolumeBreakoutStrategy",
    "SimpleBacktest",
    "Logger",
    "Config",
]
