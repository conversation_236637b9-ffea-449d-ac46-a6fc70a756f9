"""
Data fetcher module for retrieving market data from external APIs.

This module implements the DataFetcher interface and provides concrete
implementations for different data sources like VNStock API.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import pandas as pd
import requests
import time
import random
from functools import wraps
from ..utils.logger import Logger


def rate_limit(calls: int = 1, period: float = 1.0, jitter: float = 0.1):
    """Decorator to rate limit function calls."""
    min_interval = period / calls
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            delay = max(0, min_interval - elapsed)
            
            if jitter > 0:
                delay += random.uniform(0, jitter * min_interval)
                
            if delay > 0:
                time.sleep(delay)
                
            result = func(*args, **kwargs)
            last_called[0] = time.time()
            return result
        return wrapper
    return decorator


class DataFetcher(ABC):
    """Abstract base class for data fetchers."""
    
    @abstractmethod
    def fetch(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch data for a single symbol."""
        pass
    
    @abstractmethod
    def fetch_batch(
        self, symbols: List[str], start_date: str, end_date: str
    ) -> Dict[str, pd.DataFrame]:
        """Fetch data for multiple symbols."""
        pass


class VNStockFetcher(DataFetcher):
    """VNStock API data fetcher implementation."""
    
    def __init__(self, base_url: str = "https://api.vnstock.vn", timeout: int = 30):
        self.base_url = base_url
        self.timeout = timeout
        self.logger = Logger(__name__)
        self.session = requests.Session()
        
    @rate_limit(calls=10, period=60)  # 10 calls per minute
    def fetch(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch OHLCV data for a single symbol."""
        try:
            url = f"{self.base_url}/stock/{symbol}/historical"
            params = {
                "start_date": start_date,
                "end_date": end_date,
                "resolution": "1D"
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            df = pd.DataFrame(data)
            
            # Standardize column names
            df = self._standardize_columns(df)
            
            self.logger.info(f"Fetched {len(df)} records for {symbol}")
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def fetch_batch(
        self, symbols: List[str], start_date: str, end_date: str
    ) -> Dict[str, pd.DataFrame]:
        """Fetch data for multiple symbols."""
        results = {}
        
        for symbol in symbols:
            try:
                df = self.fetch(symbol, start_date, end_date)
                results[symbol] = df
            except Exception as e:
                self.logger.error(f"Error fetching batch data for {symbol}: {str(e)}")
                results[symbol] = pd.DataFrame()
                
        return results
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize column names to OHLCV format."""
        column_mapping = {
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume',
            'date': 'Date'
        }
        
        df = df.rename(columns=column_mapping)
        
        # Ensure Date column is datetime
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
            df = df.set_index('Date')
            
        return df
