# Core dependencies for Augment Raptor

# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0

# Database and storage
duckdb>=0.8.0

# HTTP requests and API clients
requests>=2.28.0

# Configuration management
PyYAML>=6.0

# Command line interface
click>=8.0.0

# Visualization (optional but recommended)
matplotlib>=3.5.0
plotly>=5.0.0

# Date and time utilities
python-dateutil>=2.8.0

# Logging and monitoring
structlog>=22.0.0

# Performance and optimization
numba>=0.56.0

# Testing utilities (for basic validation)
pytest>=7.0.0

# Data validation
pydantic>=1.10.0

# Progress bars for CLI
tqdm>=4.64.0

# Memory profiling (optional)
memory-profiler>=0.60.0

# Caching utilities
diskcache>=5.4.0

# Mathematical operations
scipy>=1.9.0

# Financial calculations
QuantLib-Python>=1.29

# Vietnamese stock market data
vnstock>=0.2.0
