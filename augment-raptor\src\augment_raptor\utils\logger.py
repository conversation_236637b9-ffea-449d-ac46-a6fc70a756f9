"""
Logging utilities module.

This module provides centralized logging functionality with support
for different log levels, file output, and structured logging.
"""

import logging
import sys
import os
from typing import Optional, Dict, Any
from pathlib import Path
from datetime import datetime


class Logger:
    """Centralized logging utility."""

    _loggers: Dict[str, logging.Logger] = {}
    _configured = False

    def __init__(self, name: str, log_level: int = logging.INFO):
        """Initialize logger for a specific module.

        Args:
            name: Logger name (usually module name)
            log_level: Logging level
        """
        self.name = name
        self.log_level = log_level

        # Configure logging if not already done
        if not Logger._configured:
            self._configure_logging()

        # Get or create logger
        if name not in Logger._loggers:
            Logger._loggers[name] = self._create_logger(name)

        self.logger = Logger._loggers[name]

    def _configure_logging(self) -> None:
        """Configure global logging settings."""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

        # Clear existing handlers
        root_logger.handlers.clear()

        # Create formatters
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        )

        simple_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)

        # File handler for all logs
        log_file = log_dir / f"augment_raptor_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)

        # Error file handler
        error_log_file = log_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.FileHandler(error_log_file, encoding="utf-8")
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_handler)

        Logger._configured = True

    def _create_logger(self, name: str) -> logging.Logger:
        """Create a new logger instance.

        Args:
            name: Logger name

        Returns:
            Configured logger instance
        """
        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)
        return logger

    def debug(self, message: str, **kwargs) -> None:
        """Log debug message.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        self._log(logging.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs) -> None:
        """Log info message.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        self._log(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs) -> None:
        """Log warning message.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        self._log(logging.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs) -> None:
        """Log error message.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        self._log(logging.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs) -> None:
        """Log critical message.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        self._log(logging.CRITICAL, message, **kwargs)

    def exception(self, message: str, **kwargs) -> None:
        """Log exception with traceback.

        Args:
            message: Log message
            **kwargs: Additional context data
        """
        if kwargs:
            message = f"{message} - Context: {kwargs}"
        self.logger.exception(message)

    def _log(self, level: int, message: str, **kwargs) -> None:
        """Internal logging method with context.

        Args:
            level: Logging level
            message: Log message
            **kwargs: Additional context data
        """
        if kwargs:
            # Format context data
            context_str = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            message = f"{message} | {context_str}"

        self.logger.log(level, message)

    def set_level(self, level: int) -> None:
        """Set logging level.

        Args:
            level: New logging level
        """
        self.logger.setLevel(level)
        self.log_level = level

    @classmethod
    def configure_from_config(cls, config: Dict[str, Any]) -> None:
        """Configure logging from configuration dictionary.

        Args:
            config: Logging configuration
        """
        # Set global log level
        log_level = config.get("level", "INFO").upper()
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
        }

        root_logger = logging.getLogger()
        root_logger.setLevel(level_map.get(log_level, logging.INFO))

        # Configure file logging
        if config.get("file_logging", True):
            log_dir = Path(config.get("log_dir", "logs"))
            log_dir.mkdir(exist_ok=True)

            # Update file handlers
            for handler in root_logger.handlers:
                if isinstance(handler, logging.FileHandler):
                    handler.setLevel(level_map.get(log_level, logging.INFO))

        # Configure console logging
        console_level = config.get("console_level", "INFO").upper()
        for handler in root_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(
                handler, logging.FileHandler
            ):
                handler.setLevel(level_map.get(console_level, logging.INFO))

    @classmethod
    def get_logger(cls, name: str) -> "Logger":
        """Get logger instance by name.

        Args:
            name: Logger name

        Returns:
            Logger instance
        """
        return cls(name)

    def log_performance(self, operation: str, duration: float, **kwargs) -> None:
        """Log performance metrics.

        Args:
            operation: Operation name
            duration: Duration in seconds
            **kwargs: Additional metrics
        """
        metrics = {"operation": operation, "duration_seconds": duration}
        metrics.update(kwargs)

        self.info(f"Performance: {operation} completed", **metrics)

    def log_trade(self, action: str, symbol: str, price: float, quantity: float, **kwargs) -> None:
        """Log trading activity.

        Args:
            action: Trade action (buy/sell)
            symbol: Trading symbol
            price: Trade price
            quantity: Trade quantity
            **kwargs: Additional trade data
        """
        trade_data = {"action": action, "symbol": symbol, "price": price, "quantity": quantity}
        trade_data.update(kwargs)

        self.info(f"Trade executed: {action} {quantity} {symbol} @ {price}", **trade_data)

    def log_strategy_signal(
        self, strategy: str, symbol: str, signal: str, strength: float, **kwargs
    ) -> None:
        """Log strategy signals.

        Args:
            strategy: Strategy name
            symbol: Trading symbol
            signal: Signal type (buy/sell/hold)
            strength: Signal strength
            **kwargs: Additional signal data
        """
        signal_data = {
            "strategy": strategy,
            "symbol": symbol,
            "signal": signal,
            "strength": strength,
        }
        signal_data.update(kwargs)

        self.info(f"Strategy signal: {strategy} - {signal} {symbol}", **signal_data)
