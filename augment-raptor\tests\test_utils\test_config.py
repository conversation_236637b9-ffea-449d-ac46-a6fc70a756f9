"""
Tests for utils.config module.
"""

import pytest
import tempfile
import os
import yaml
from unittest.mock import Mock, patch, mock_open

from augment_raptor.utils.config import Config


class TestConfig:
    """Test Config class functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create temporary directory for config files
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test config files
        self.default_config = {
            'app': {
                'name': 'Augment Raptor',
                'version': '1.0.0'
            },
            'database': {
                'host': 'localhost',
                'port': 5432,
                'name': 'default_db'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
        
        self.dev_config = {
            'database': {
                'name': 'dev_db',
                'debug': True
            },
            'logging': {
                'level': 'DEBUG'
            }
        }
        
        self.prod_config = {
            'database': {
                'name': 'prod_db',
                'host': 'prod-server'
            },
            'logging': {
                'level': 'WARNING'
            }
        }
        
        # Write config files
        with open(os.path.join(self.temp_dir, 'default.yaml'), 'w') as f:
            yaml.dump(self.default_config, f)
            
        with open(os.path.join(self.temp_dir, 'development.yaml'), 'w') as f:
            yaml.dump(self.dev_config, f)
            
        with open(os.path.join(self.temp_dir, 'production.yaml'), 'w') as f:
            yaml.dump(self.prod_config, f)
            
    def teardown_method(self):
        """Clean up test fixtures."""
        # Remove temporary files
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)
        
    def test_load_default_config(self):
        """Test loading default configuration."""
        config = Config(config_dir=self.temp_dir, environment='default')
        
        assert config.get('app.name') == 'Augment Raptor'
        assert config.get('app.version') == '1.0.0'
        assert config.get('database.host') == 'localhost'
        assert config.get('database.port') == 5432
        
    def test_load_development_config(self):
        """Test loading development configuration with inheritance."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        # Should inherit from default
        assert config.get('app.name') == 'Augment Raptor'
        assert config.get('database.host') == 'localhost'
        assert config.get('database.port') == 5432
        
        # Should override with dev values
        assert config.get('database.name') == 'dev_db'
        assert config.get('database.debug') is True
        assert config.get('logging.level') == 'DEBUG'
        
    def test_load_production_config(self):
        """Test loading production configuration with inheritance."""
        config = Config(config_dir=self.temp_dir, environment='production')
        
        # Should inherit from default
        assert config.get('app.name') == 'Augment Raptor'
        assert config.get('database.port') == 5432
        
        # Should override with prod values
        assert config.get('database.name') == 'prod_db'
        assert config.get('database.host') == 'prod-server'
        assert config.get('logging.level') == 'WARNING'
        
    def test_get_with_default_value(self):
        """Test get method with default value."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        # Existing key
        assert config.get('app.name', 'default') == 'Augment Raptor'
        
        # Non-existing key with default
        assert config.get('nonexistent.key', 'default_value') == 'default_value'
        
        # Non-existing key without default
        assert config.get('nonexistent.key') is None
        
    def test_get_nested_keys(self):
        """Test getting nested configuration values."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        # Dot notation
        assert config.get('database.host') == 'localhost'
        assert config.get('database.name') == 'dev_db'
        
        # Deep nesting
        assert config.get('app.name') == 'Augment Raptor'
        
    def test_get_section(self):
        """Test getting entire configuration section."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        database_config = config.get('database')
        
        assert isinstance(database_config, dict)
        assert database_config['host'] == 'localhost'
        assert database_config['name'] == 'dev_db'
        assert database_config['debug'] is True
        
    def test_set_value(self):
        """Test setting configuration values."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        # Set new value
        config.set('new.key', 'new_value')
        assert config.get('new.key') == 'new_value'
        
        # Override existing value
        config.set('app.name', 'Modified Name')
        assert config.get('app.name') == 'Modified Name'
        
    def test_get_all(self):
        """Test getting all configuration as dictionary."""
        config = Config(config_dir=self.temp_dir, environment='development')

        config_dict = config.get_all()

        assert isinstance(config_dict, dict)
        assert 'app' in config_dict
        assert 'database' in config_dict
        assert config_dict['app']['name'] == 'Augment Raptor'
        assert config_dict['database']['name'] == 'dev_db'
        
    def test_environment_variable_override(self):
        """Test environment variable override."""
        with patch.dict(os.environ, {'AUGMENT_RAPTOR_DATABASE_HOST': 'env-host'}):
            config = Config(config_dir=self.temp_dir, environment='development')

            # Should be overridden by environment variable
            assert config.get('database.host') == 'env-host'
            
    def test_missing_config_file(self):
        """Test handling missing configuration file."""
        # Config should work even without environment-specific file
        # It will just use default config
        config = Config(config_dir=self.temp_dir, environment='nonexistent')
        assert config.get('app.name') == 'Augment Raptor'  # From default config
            
    def test_invalid_yaml_file(self):
        """Test handling invalid YAML file."""
        # Create invalid YAML file
        invalid_file = os.path.join(self.temp_dir, 'invalid.yaml')
        with open(invalid_file, 'w') as f:
            f.write('invalid: yaml: content: [')
            
        with pytest.raises(yaml.YAMLError):
            Config(config_dir=self.temp_dir, environment='invalid')
            
    def test_config_validation(self):
        """Test configuration validation."""
        config = Config(config_dir=self.temp_dir, environment='development')

        # Test validate_required_keys method
        required_keys = ['app.name', 'database.host']

        # Should not raise exception for existing keys
        config.validate_required_keys(required_keys)

        # Should raise exception for missing keys
        with pytest.raises(ValueError):
            config.validate_required_keys(['nonexistent.key'])
        
    def test_config_update(self):
        """Test configuration update functionality."""
        config = Config(config_dir=self.temp_dir, environment='development')

        original_name = config.get('app.name')
        assert original_name == 'Augment Raptor'

        # Update configuration
        update_dict = {'app': {'name': 'Modified App', 'version': '2.0.0'}}
        config.update(update_dict)

        # Should reflect the change
        assert config.get('app.name') == 'Modified App'
        assert config.get('app.version') == '2.0.0'

    def test_config_helper_methods(self):
        """Test configuration helper methods."""
        config = Config(config_dir=self.temp_dir, environment='development')

        # Test specific config getters
        data_config = config.get_data_config()
        assert isinstance(data_config, dict)

        strategy_config = config.get_strategy_config()
        assert isinstance(strategy_config, dict)

        backtest_config = config.get_backtest_config()
        assert isinstance(backtest_config, dict)

        logging_config = config.get_logging_config()
        assert isinstance(logging_config, dict)
        assert logging_config.get('level') == 'DEBUG'  # From dev config

    def test_config_save(self):
        """Test configuration save functionality."""
        config = Config(config_dir=self.temp_dir, environment='development')

        # Save to temporary file
        save_path = os.path.join(self.temp_dir, 'saved_config.yaml')
        config.save(save_path)

        # Verify file was created and contains expected data
        assert os.path.exists(save_path)

        with open(save_path, 'r') as f:
            saved_config = yaml.safe_load(f)

        assert saved_config['app']['name'] == 'Augment Raptor'
        assert saved_config['database']['name'] == 'dev_db'
        
    def test_config_merge_deep(self):
        """Test deep merging of configuration dictionaries."""
        config = Config(config_dir=self.temp_dir, environment='development')
        
        # Test that nested dictionaries are properly merged
        # Default has database.host and database.port
        # Development overrides database.name and adds database.debug
        # All should be present in final config
        
        database_config = config.get('database')
        assert 'host' in database_config  # From default
        assert 'port' in database_config  # From default
        assert 'name' in database_config  # Overridden in dev
        assert 'debug' in database_config  # Added in dev
        
    def test_config_with_lists_and_complex_types(self):
        """Test configuration with lists and complex data types."""
        # Add complex config
        complex_config = {
            'features': {
                'enabled': ['feature1', 'feature2', 'feature3'],
                'disabled': ['feature4'],
                'settings': {
                    'feature1': {'param1': 'value1', 'param2': 42},
                    'feature2': {'param1': 'value2', 'param2': 84}
                }
            }
        }
        
        with open(os.path.join(self.temp_dir, 'complex.yaml'), 'w') as f:
            yaml.dump(complex_config, f)
            
        config = Config(config_dir=self.temp_dir, environment='complex')
        
        # Test list access
        enabled_features = config.get('features.enabled')
        assert isinstance(enabled_features, list)
        assert 'feature1' in enabled_features
        
        # Test nested dict access
        feature1_settings = config.get('features.settings.feature1')
        assert isinstance(feature1_settings, dict)
        assert feature1_settings['param1'] == 'value1'
        assert feature1_settings['param2'] == 42


class TestConfigErrorHandling:
    """Test Config error handling scenarios."""
    
    def test_config_with_empty_directory(self):
        """Test Config with empty directory."""
        empty_dir = tempfile.mkdtemp()

        try:
            # Config should work even with empty directory
            config = Config(config_dir=empty_dir, environment='development')
            assert config.get_all() == {}  # Should be empty config
        finally:
            os.rmdir(empty_dir)
            
    def test_config_with_permission_error(self):
        """Test Config with permission errors."""
        # This test is platform-specific and might not work on all systems
        # Skip if we can't create the test condition
        pass
        
    def test_config_get_with_invalid_key_format(self):
        """Test get method with invalid key format."""
        config = Config(config_dir=tempfile.mkdtemp(), environment='development')
        
        # Empty key
        assert config.get('') is None
        
        # Key with only dots
        assert config.get('...') is None
        
        # Key starting/ending with dots
        assert config.get('.key') is None
        assert config.get('key.') is None


class TestConfigIntegration:
    """Test Config integration scenarios."""
    
    def test_config_with_real_world_structure(self):
        """Test Config with realistic configuration structure."""
        temp_dir = tempfile.mkdtemp()
        
        real_config = {
            'app': {
                'name': 'Augment Raptor',
                'version': '1.0.0',
                'debug': False
            },
            'data': {
                'sources': {
                    'vnstock': {
                        'base_url': 'https://api.vnstock.vn',
                        'timeout': 30,
                        'rate_limit': 100
                    }
                },
                'cache': {
                    'memory': {
                        'max_size': 1000,
                        'ttl': 3600
                    },
                    'disk': {
                        'path': './cache',
                        'max_size': '1GB'
                    }
                }
            },
            'strategies': {
                'volume_breakout': {
                    'volume_threshold': 2.0,
                    'price_change_threshold': 0.05,
                    'lookback_period': 20
                }
            },
            'risk_management': {
                'max_position_size': 0.1,
                'stop_loss': 0.02,
                'take_profit': 0.06
            }
        }
        
        try:
            with open(os.path.join(temp_dir, 'default.yaml'), 'w') as f:
                yaml.dump(real_config, f)
                
            config = Config(config_dir=temp_dir, environment='default')
            
            # Test various access patterns
            assert config.get('app.name') == 'Augment Raptor'
            assert config.get('data.sources.vnstock.timeout') == 30
            assert config.get('strategies.volume_breakout.volume_threshold') == 2.0
            assert config.get('risk_management.max_position_size') == 0.1
            
            # Test getting sections
            vnstock_config = config.get('data.sources.vnstock')
            assert vnstock_config['base_url'] == 'https://api.vnstock.vn'
            
            cache_config = config.get('data.cache')
            assert 'memory' in cache_config
            assert 'disk' in cache_config
            
        finally:
            # Cleanup
            for file in os.listdir(temp_dir):
                os.remove(os.path.join(temp_dir, file))
            os.rmdir(temp_dir)
