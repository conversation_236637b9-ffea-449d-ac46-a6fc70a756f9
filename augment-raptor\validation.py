import sys
sys.path.insert(0, 'src')

print('Sprint 2: Strategy & Backtest - Validation Report')
print('=' * 60)

# Task 2.1 Validation
print('\nTask 2.1: Volume Breakout Strategy')
try:
    from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
    strategy = VolumeBreakoutStrategy()
    print(f'SUCCESS: Strategy created: {strategy.name}')
    task_2_1 = True
except Exception as e:
    print(f'FAILED: {e}')
    task_2_1 = False

# Task 2.2 Validation  
print('\nTask 2.2: Simple Backtest Engine')
try:
    from augment_raptor.backtest.simple_backtest import SimpleBacktest
    backtest = SimpleBacktest(initial_capital=100000)
    print(f'SUCCESS: Backtest created')
    task_2_2 = True
except Exception as e:
    print(f'FAILED: {e}')
    task_2_2 = False

# Task 2.3 Validation
print('\nTask 2.3: CLI Implementation')
try:
    import augment_raptor.cli.commands as cli_module
    print('SUCCESS: CLI module imported')
    task_2_3 = True
except Exception as e:
    print(f'FAILED: {e}')
    task_2_3 = False

# Overall Assessment
completed = sum([task_2_1, task_2_2, task_2_3])
completion_rate = (completed / 3) * 100

print(f'\nCompleted Tasks: {completed}/3')
print(f'Completion Rate: {completion_rate:.1f}%')

if completion_rate >= 80:
    print('SUCCESS: Sprint 2 PASSED')
else:
    print('PARTIAL: Sprint 2 needs attention')
