# Augment Raptor - Algorithmic Trading System

[![Version](https://img.shields.io/badge/version-0.1.0-green.svg)](https://github.com/augment/augment-raptor/releases)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Test Coverage](https://img.shields.io/badge/coverage-62%25-orange.svg)](https://github.com/augment/augment-raptor)
[![Tests](https://img.shields.io/badge/tests-105%20passed-brightgreen.svg)](https://github.com/augment/augment-raptor)

Augment Raptor là một hệ thống giao dịch thuật toán đ<PERSON>ợc thiết kế để phát hiện và khai thác các cơ hội giao dịch trên thị trường chứng khoán Việt Nam sử dụng chiến lược Volume Breakout.

## 🎯 Tính năng chính

- **Chiến lược Volume Breakout**: Phát hiện đột phá khối lượng kết hợp với đột phá giá
- **Backtesting Engine**: Kiểm thử hiệu suất chiến lược trên dữ liệu lịch sử
- **Market Scanner**: Quét thị trường tự động để tìm cơ hội giao dịch
- **Caching thông minh**: Hệ thống cache đa cấp với DuckDB và Memory
- **CLI Interface**: Giao diện dòng lệnh thân thiện
- **Kiến trúc Module**: Thiết kế linh hoạt, dễ mở rộng

## 🎉 Sprint 3 Completed - Production Ready!

**Augment Raptor v0.1.0** hiện đã sẵn sàng cho production với:

- ✅ **Comprehensive Testing**: 115 tests với 91.3% success rate (105 passed, 10 skipped, 0 failed)
- ✅ **Test Coverage**: 62% coverage across all modules
- ✅ **Integration Testing**: End-to-end workflow validation
- ✅ **Professional Documentation**: Complete API reference và user guides
- ✅ **Quality Assurance**: Automated testing, linting, và formatting
- ✅ **Performance Optimization**: Efficient processing với caching systems

## 🏗️ Kiến trúc hệ thống

```
augment-raptor/
├── src/augment_raptor/          # Mã nguồn chính
│   ├── data/                    # Module quản lý dữ liệu
│   ├── strategies/              # Module chiến lược giao dịch
│   ├── backtest/               # Module backtest
│   ├── cli/                    # Giao diện dòng lệnh
│   └── utils/                  # Tiện ích
├── config/                     # File cấu hình
├── scripts/                    # Script chạy chính
├── tests/                      # Unit tests
└── docs/                       # Tài liệu
```

## 🚀 Cài đặt

### Yêu cầu hệ thống

- Python 3.9 hoặc cao hơn
- pip hoặc conda
- Git

### Cài đặt từ source

```bash
# Clone repository
git clone https://github.com/augment/augment-raptor.git
cd augment-raptor

# Tạo virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows

# Cài đặt dependencies
pip install -r requirements.txt

# Cài đặt package ở chế độ development
pip install -e .
```

### Cài đặt development environment

```bash
# Cài đặt development dependencies
pip install -r requirements-dev.txt

# Cài đặt pre-commit hooks
pre-commit install
```

## 📖 Sử dụng

### CLI Commands

#### 1. Quét thị trường

```bash
# Quét các mã mặc định
augment-raptor scan

# Quét mã cụ thể
augment-raptor scan --symbols VIC VHM VCB

# Quét với khoảng thời gian tùy chỉnh
augment-raptor scan --start-date 2024-01-01 --end-date 2024-12-31

# Xuất kết quả ra file
augment-raptor scan --output results.json --format json
```

#### 2. Chạy backtest

```bash
# Backtest cơ bản
augment-raptor backtest --symbol VIC --start-date 2024-01-01 --end-date 2024-12-31

# Backtest với vốn tùy chỉnh
augment-raptor backtest --symbol VIC --start-date 2024-01-01 --end-date 2024-12-31 --initial-capital 200000

# Backtest với biểu đồ
augment-raptor backtest --symbol VIC --start-date 2024-01-01 --end-date 2024-12-31 --plot
```

#### 3. Cập nhật dữ liệu

```bash
# Cập nhật tất cả mã
augment-raptor update-data

# Cập nhật mã cụ thể
augment-raptor update-data --symbol VIC

# Cập nhật với số ngày tùy chỉnh
augment-raptor update-data --days 60
```

### Sử dụng Scripts

```bash
# Chạy scanner
python scripts/run_scanner.py --symbols VIC VHM VCB

# Chạy backtest
python scripts/run_backtest.py --symbol VIC --start-date 2024-01-01 --end-date 2024-12-31
```

### Sử dụng như Python Library

```python
from augment_raptor.data.fetcher import VNStockFetcher
from augment_raptor.data.processor import DataProcessor
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
from augment_raptor.backtest.simple_backtest import SimpleBacktest

# Khởi tạo components
fetcher = VNStockFetcher()
processor = DataProcessor()
strategy = VolumeBreakoutStrategy()
backtest = SimpleBacktest()

# Lấy và xử lý dữ liệu
data = fetcher.fetch("VIC", "2024-01-01", "2024-12-31")
processed_data = processor.process(data)

# Phân tích chiến lược
analysis = strategy.analyze(processed_data)
print(f"Tìm thấy {analysis['total_signals']} tín hiệu")

# Chạy backtest
results = backtest.run(strategy, processed_data)
print(f"Tổng lợi nhuận: {results['total_return']:.2%}")
```

## ⚙️ Cấu hình

### File cấu hình

- `config/default.yaml`: Cấu hình mặc định
- `config/development.yaml`: Cấu hình môi trường phát triển
- `config/production.yaml`: Cấu hình môi trường sản xuất

### Tùy chỉnh chiến lược Volume Breakout

```yaml
strategy:
  volume_breakout:
    volume_threshold: 2.0      # Ngưỡng khối lượng (2x trung bình)
    price_threshold: 0.02      # Ngưỡng thay đổi giá (2%)
    volume_period: 20          # Chu kỳ trung bình khối lượng
    stop_loss_atr: 2.0        # Stop loss (2 ATR)
    take_profit_atr: 4.0      # Take profit (4 ATR)
    min_volume: 100000        # Khối lượng tối thiểu
    trend_filter: true        # Bộ lọc xu hướng
```

## 🧪 Testing

### Test Coverage & Quality
- **Total Tests**: 115 test cases
- **Success Rate**: 91.3% (105 passed, 10 skipped, 0 failed)
- **Coverage**: 62% across all modules
- **Test Types**: Unit tests, Integration tests, Performance tests

```bash
# Chạy tất cả tests
pytest

# Chạy tests với coverage
pytest --cov=src/augment_raptor --cov-report=html

# Chạy tests cụ thể
pytest tests/test_strategies/

# Chạy integration tests
pytest tests/test_integration_advanced.py

# Chạy tests performance
pytest -m "not slow"
```

## 📊 Metrics và Báo cáo

Hệ thống cung cấp các metrics chi tiết:

### Trading Metrics
- Win Rate (Tỷ lệ thắng)
- Profit Factor (Hệ số lợi nhuận)
- Average Win/Loss (Lãi/lỗ trung bình)
- Maximum Consecutive Wins/Losses

### Risk Metrics
- Sharpe Ratio
- Sortino Ratio
- Calmar Ratio
- Maximum Drawdown
- Value at Risk (VaR)

### Performance Metrics
- Total Return (Tổng lợi nhuận)
- Annualized Return (Lợi nhuận hàng năm)
- Volatility (Độ biến động)
- Average Holding Period

## 🔧 Development

### Code Quality

Dự án sử dụng các công cụ đảm bảo chất lượng code:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pytest**: Testing
- **pre-commit**: Git hooks

### Contribution Guidelines

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📝 Roadmap

### Phase 1 (MVP + Testing & Documentation) - ✅ Hoàn thành
- [x] Cấu trúc dự án cơ bản
- [x] Data fetcher và processor
- [x] Volume Breakout strategy
- [x] Simple backtest engine
- [x] CLI interface
- [x] Comprehensive unit testing (62% coverage, 115 tests)
- [x] Integration testing & end-to-end validation
- [x] Professional documentation & API reference
- [x] Release preparation for v0.1.0

### Phase 2 - 📋 Planned
- [ ] Advanced backtest engine
- [ ] Multiple strategies support
- [ ] Real-time data streaming
- [ ] Web dashboard
- [ ] Portfolio management

### Phase 3 - Tương lai
- [ ] Machine learning integration
- [ ] Multi-market support
- [ ] Advanced risk management
- [ ] Cloud deployment
- [ ] Mobile app

## 📄 License

Dự án này được phân phối dưới giấy phép MIT. Xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 🤝 Support

- 📧 Email: <EMAIL>
- 🐛 Bug Reports: [GitHub Issues](https://github.com/augment/augment-raptor/issues)
- 📖 Documentation: [Wiki](https://github.com/augment/augment-raptor/wiki)

## 🙏 Acknowledgments

- VNStock API cho dữ liệu thị trường
- Pandas và NumPy cho xử lý dữ liệu
- DuckDB cho storage hiệu suất cao
- Click cho CLI framework
