#!/usr/bin/env python3
"""
System validation script for Augment Raptor

This script validates that the entire system is working correctly
by running comprehensive tests and checks.
"""

import sys
import os
from pathlib import Path
import traceback

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def validate_project_structure():
    """Validate that all required files and directories exist."""
    print("🔍 Validating project structure...")
    
    required_files = [
        "src/augment_raptor/__init__.py",
        "src/augment_raptor/data/__init__.py",
        "src/augment_raptor/data/fetcher.py",
        "src/augment_raptor/data/processor.py",
        "src/augment_raptor/data/storage.py",
        "src/augment_raptor/strategies/__init__.py",
        "src/augment_raptor/strategies/base.py",
        "src/augment_raptor/strategies/volume_breakout.py",
        "src/augment_raptor/backtest/__init__.py",
        "src/augment_raptor/backtest/simple_backtest.py",
        "src/augment_raptor/backtest/metrics.py",
        "src/augment_raptor/cli/__init__.py",
        "src/augment_raptor/cli/commands.py",
        "src/augment_raptor/utils/__init__.py",
        "src/augment_raptor/utils/config.py",
        "src/augment_raptor/utils/logger.py",
        "src/augment_raptor/utils/helpers.py",
        "config/default.yaml",
        "config/development.yaml",
        "config/production.yaml",
        "requirements.txt",
        "requirements-dev.txt",
        "setup.py",
        "pyproject.toml",
        "README.md",
        ".gitignore",
        ".pre-commit-config.yaml",
        "main.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ Project structure validation passed")
    return True


def validate_imports():
    """Validate that all modules can be imported."""
    print("🔍 Validating module imports...")
    
    try:
        # Test main package import
        import augment_raptor
        print(f"✅ Main package: {augment_raptor.__version__}")
        
        # Test data modules
        from augment_raptor.data.fetcher import DataFetcher, VNStockFetcher
        from augment_raptor.data.processor import DataProcessor
        from augment_raptor.data.storage import Cache, MemoryCache, DuckDBCache
        print("✅ Data modules imported successfully")
        
        # Test strategy modules
        from augment_raptor.strategies.base import Strategy
        from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
        print("✅ Strategy modules imported successfully")
        
        # Test backtest modules
        from augment_raptor.backtest.simple_backtest import SimpleBacktest
        from augment_raptor.backtest.metrics import PerformanceMetrics
        print("✅ Backtest modules imported successfully")
        
        # Test utils modules
        from augment_raptor.utils.config import Config
        from augment_raptor.utils.logger import Logger
        from augment_raptor.utils.helpers import generate_cache_key
        print("✅ Utils modules imported successfully")
        
        # Test CLI modules
        from augment_raptor.cli.commands import cli
        print("✅ CLI modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        traceback.print_exc()
        return False


def validate_configuration():
    """Validate configuration system."""
    print("🔍 Validating configuration system...")
    
    try:
        from augment_raptor.utils.config import Config
        
        # Test default config
        config = Config(config_dir="config", environment="development")
        
        # Test basic config access
        app_name = config.get("app.name")
        if app_name != "Augment Raptor":
            print(f"❌ Config error: Expected 'Augment Raptor', got '{app_name}'")
            return False
        
        # Test strategy config
        strategy_config = config.get_strategy_config()
        if "volume_breakout" not in strategy_config:
            print("❌ Config error: volume_breakout strategy config missing")
            return False
        
        print("✅ Configuration system validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {str(e)}")
        traceback.print_exc()
        return False


def validate_data_processing():
    """Validate data processing functionality."""
    print("🔍 Validating data processing...")
    
    try:
        import pandas as pd
        import numpy as np
        from augment_raptor.data.processor import DataProcessor
        
        processor = DataProcessor()
        
        # Create sample data
        sample_data = pd.DataFrame({
            'Open': [100, 102, 101, 103, 105],
            'High': [105, 106, 104, 107, 108],
            'Low': [98, 100, 99, 101, 103],
            'Close': [102, 101, 103, 105, 107],
            'Volume': [1000000, 1200000, 800000, 1500000, 2000000]
        })
        
        # Process data
        processed_data = processor.process(sample_data)
        
        # Validate indicators
        required_indicators = ['Price_Change_Pct', 'ATR_14', 'Volume_SMA_20']
        for indicator in required_indicators:
            if indicator not in processed_data.columns:
                print(f"❌ Missing indicator: {indicator}")
                return False
        
        print("✅ Data processing validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Data processing error: {str(e)}")
        traceback.print_exc()
        return False


def validate_strategy():
    """Validate strategy functionality."""
    print("🔍 Validating strategy functionality...")
    
    try:
        import pandas as pd
        import numpy as np
        from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
        
        strategy = VolumeBreakoutStrategy()
        
        # Test strategy properties
        if strategy.name != "Volume Breakout":
            print(f"❌ Strategy name error: Expected 'Volume Breakout', got '{strategy.name}'")
            return False
        
        # Create sample data with enough rows for strategy validation
        np.random.seed(42)
        n_rows = 60  # More than minimum required (50)

        # Generate realistic price data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        sample_data = pd.DataFrame({
            'Open': prices[:-1],
            'High': [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
            'Low': [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
            'Close': prices[1:],
            'Volume': [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)]
        })
        
        # Test data validation
        if not strategy.validate_data(sample_data):
            print("❌ Strategy data validation failed")
            return False
        
        # Test signal generation
        signals = strategy.generate_signals(sample_data)
        required_columns = ['signal', 'signal_strength', 'entry_price']
        for col in required_columns:
            if col not in signals.columns:
                print(f"❌ Missing signal column: {col}")
                return False
        
        print("✅ Strategy validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Strategy error: {str(e)}")
        traceback.print_exc()
        return False


def validate_backtest():
    """Validate backtest functionality."""
    print("🔍 Validating backtest functionality...")
    
    try:
        import pandas as pd
        import numpy as np
        from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
        from augment_raptor.backtest.simple_backtest import SimpleBacktest
        
        strategy = VolumeBreakoutStrategy()
        backtest = SimpleBacktest(initial_capital=100000)
        
        # Create sample data with dates - enough for strategy validation
        n_rows = 60
        dates = pd.date_range('2024-01-01', periods=n_rows, freq='D')

        # Generate realistic data
        np.random.seed(42)
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        sample_data = pd.DataFrame({
            'Open': prices[:-1],
            'High': [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
            'Low': [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
            'Close': prices[1:],
            'Volume': [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)]
        }, index=dates[:-1])  # Use n_rows-1 dates to match data length
        
        # Run backtest
        results = backtest.run(strategy, sample_data)
        
        # Validate results
        required_keys = ['strategy', 'total_return', 'final_capital', 'initial_capital']
        for key in required_keys:
            if key not in results:
                print(f"❌ Missing backtest result: {key}")
                return False
        
        if results['initial_capital'] != 100000:
            print("❌ Backtest initial capital mismatch")
            return False
        
        print("✅ Backtest validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Backtest error: {str(e)}")
        traceback.print_exc()
        return False


def validate_cli():
    """Validate CLI functionality."""
    print("🔍 Validating CLI functionality...")
    
    try:
        from augment_raptor.cli.commands import cli
        
        # Test that CLI can be imported and has expected commands
        if not hasattr(cli, 'commands'):
            print("❌ CLI commands not found")
            return False
        
        print("✅ CLI validation passed")
        return True
        
    except Exception as e:
        print(f"❌ CLI error: {str(e)}")
        traceback.print_exc()
        return False


def main():
    """Run all validation tests."""
    print("🚀 Starting Augment Raptor System Validation")
    print("=" * 50)
    
    validations = [
        ("Project Structure", validate_project_structure),
        ("Module Imports", validate_imports),
        ("Configuration System", validate_configuration),
        ("Data Processing", validate_data_processing),
        ("Strategy Functionality", validate_strategy),
        ("Backtest Engine", validate_backtest),
        ("CLI Interface", validate_cli),
    ]
    
    passed = 0
    total = len(validations)
    
    for name, validation_func in validations:
        print(f"\n📋 {name}")
        print("-" * 30)
        
        try:
            if validation_func():
                passed += 1
            else:
                print(f"❌ {name} validation failed")
        except Exception as e:
            print(f"❌ {name} validation crashed: {str(e)}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"📊 VALIDATION SUMMARY")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ Augment Raptor system is ready for use")
        return True
    else:
        print("❌ Some validations failed")
        print("🔧 Please fix the issues before proceeding")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
