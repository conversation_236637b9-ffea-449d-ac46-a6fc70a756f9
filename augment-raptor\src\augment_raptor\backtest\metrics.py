"""
Performance metrics calculation for backtesting results.

This module provides comprehensive performance metrics calculation
including returns, risk metrics, and trading statistics.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
import math


class PerformanceMetrics:
    """Calculate performance metrics for backtest results."""

    def __init__(self, trades: List[Dict], equity_curve: List[Dict], initial_capital: float):
        """Initialize metrics calculator.

        Args:
            trades: List of completed trades
            equity_curve: List of daily equity values
            initial_capital: Starting capital amount
        """
        self.trades = trades
        self.equity_curve = equity_curve
        self.initial_capital = initial_capital

        # Convert to DataFrames for easier analysis
        self.trades_df = pd.DataFrame(trades) if trades else pd.DataFrame()
        self.equity_df = pd.DataFrame(equity_curve) if equity_curve else pd.DataFrame()

        if not self.equity_df.empty:
            self.equity_df["date"] = pd.to_datetime(self.equity_df["date"])
            self.equity_df = self.equity_df.set_index("date")

    def calculate_all_metrics(self) -> Dict[str, Any]:
        """Calculate all performance metrics."""
        if self.trades_df.empty or self.equity_df.empty:
            return {}

        metrics = {}

        # Basic metrics
        metrics.update(self._calculate_basic_metrics())

        # Return metrics
        metrics.update(self._calculate_return_metrics())

        # Risk metrics
        metrics.update(self._calculate_risk_metrics())

        # Trading metrics
        metrics.update(self._calculate_trading_metrics())

        # Drawdown metrics
        metrics.update(self._calculate_drawdown_metrics())

        return metrics

    def _calculate_basic_metrics(self) -> Dict[str, Any]:
        """Calculate basic performance metrics."""
        final_equity = self.equity_df["equity"].iloc[-1]
        total_return = (final_equity - self.initial_capital) / self.initial_capital

        # Time period
        start_date = self.equity_df.index[0]
        end_date = self.equity_df.index[-1]
        total_days = (end_date - start_date).days

        return {
            "total_return": float(total_return),
            "total_return_pct": float(total_return * 100),
            "final_equity": float(final_equity),
            "total_days": int(total_days),
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
        }

    def _calculate_return_metrics(self) -> Dict[str, Any]:
        """Calculate return-based metrics."""
        # Daily returns
        daily_returns = self.equity_df["equity"].pct_change().dropna()

        if daily_returns.empty:
            return {}

        # Annualized return
        total_days = len(daily_returns)
        total_return = (self.equity_df["equity"].iloc[-1] / self.initial_capital) - 1
        annualized_return = (1 + total_return) ** (252 / total_days) - 1

        # Average daily return
        avg_daily_return = daily_returns.mean()

        # Volatility (annualized)
        daily_volatility = daily_returns.std()
        annualized_volatility = daily_volatility * np.sqrt(252)

        return {
            "annualized_return": float(annualized_return),
            "annualized_return_pct": float(annualized_return * 100),
            "avg_daily_return": float(avg_daily_return),
            "avg_daily_return_pct": float(avg_daily_return * 100),
            "annualized_volatility": float(annualized_volatility),
            "annualized_volatility_pct": float(annualized_volatility * 100),
        }

    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk-based metrics."""
        daily_returns = self.equity_df["equity"].pct_change().dropna()

        if daily_returns.empty:
            return {}

        # Sharpe ratio (assuming 0% risk-free rate)
        avg_return = daily_returns.mean()
        volatility = daily_returns.std()
        sharpe_ratio = (avg_return / volatility) * np.sqrt(252) if volatility > 0 else 0

        # Sortino ratio (downside deviation)
        downside_returns = daily_returns[daily_returns < 0]
        downside_deviation = downside_returns.std() if len(downside_returns) > 0 else 0
        sortino_ratio = (
            (avg_return / downside_deviation) * np.sqrt(252) if downside_deviation > 0 else 0
        )

        # Calmar ratio (return / max drawdown)
        max_drawdown = self._calculate_max_drawdown()
        calmar_ratio = (avg_return * 252) / abs(max_drawdown) if max_drawdown != 0 else 0

        # Value at Risk (VaR) - 5% confidence level
        var_5 = np.percentile(daily_returns, 5)

        # Conditional Value at Risk (CVaR)
        cvar_5 = daily_returns[daily_returns <= var_5].mean()

        return {
            "sharpe_ratio": float(sharpe_ratio),
            "sortino_ratio": float(sortino_ratio),
            "calmar_ratio": float(calmar_ratio),
            "var_5_pct": float(var_5 * 100),
            "cvar_5_pct": float(cvar_5 * 100),
        }

    def _calculate_trading_metrics(self) -> Dict[str, Any]:
        """Calculate trading-specific metrics."""
        if self.trades_df.empty:
            return {}

        # Win/Loss statistics
        winning_trades = self.trades_df[self.trades_df["pnl"] > 0]
        losing_trades = self.trades_df[self.trades_df["pnl"] < 0]

        total_trades = len(self.trades_df)
        winning_trades_count = len(winning_trades)
        losing_trades_count = len(losing_trades)

        win_rate = winning_trades_count / total_trades if total_trades > 0 else 0

        # Average win/loss
        avg_win = winning_trades["pnl"].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades["pnl"].mean() if not losing_trades.empty else 0

        # Profit factor
        total_wins = winning_trades["pnl"].sum() if not winning_trades.empty else 0
        total_losses = abs(losing_trades["pnl"].sum()) if not losing_trades.empty else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else float("inf")

        # Average holding period
        avg_holding_days = self.trades_df["holding_days"].mean()

        # Largest win/loss
        largest_win = self.trades_df["pnl"].max()
        largest_loss = self.trades_df["pnl"].min()

        # Consecutive wins/losses
        consecutive_wins = self._calculate_consecutive_wins()
        consecutive_losses = self._calculate_consecutive_losses()

        return {
            "total_trades": int(total_trades),
            "winning_trades": int(winning_trades_count),
            "losing_trades": int(losing_trades_count),
            "win_rate": float(win_rate),
            "win_rate_pct": float(win_rate * 100),
            "avg_win": float(avg_win),
            "avg_loss": float(avg_loss),
            "profit_factor": float(profit_factor),
            "avg_holding_days": float(avg_holding_days),
            "largest_win": float(largest_win),
            "largest_loss": float(largest_loss),
            "max_consecutive_wins": int(consecutive_wins),
            "max_consecutive_losses": int(consecutive_losses),
        }

    def _calculate_drawdown_metrics(self) -> Dict[str, Any]:
        """Calculate drawdown metrics."""
        if self.equity_df.empty:
            return {}

        # Calculate running maximum
        equity_series = self.equity_df["equity"]
        running_max = equity_series.expanding().max()

        # Calculate drawdown
        drawdown = (equity_series - running_max) / running_max

        # Maximum drawdown
        max_drawdown = drawdown.min()
        max_drawdown_date = drawdown.idxmin()

        # Average drawdown
        avg_drawdown = drawdown[drawdown < 0].mean() if (drawdown < 0).any() else 0

        # Drawdown duration
        drawdown_periods = self._calculate_drawdown_periods(drawdown)
        max_drawdown_duration = max(drawdown_periods) if drawdown_periods else 0
        avg_drawdown_duration = np.mean(drawdown_periods) if drawdown_periods else 0

        return {
            "max_drawdown": float(max_drawdown),
            "max_drawdown_pct": float(max_drawdown * 100),
            "max_drawdown_date": max_drawdown_date.strftime("%Y-%m-%d"),
            "avg_drawdown": float(avg_drawdown),
            "avg_drawdown_pct": float(avg_drawdown * 100),
            "max_drawdown_duration_days": int(max_drawdown_duration),
            "avg_drawdown_duration_days": float(avg_drawdown_duration),
        }

    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown."""
        if self.equity_df.empty:
            return 0.0

        equity_series = self.equity_df["equity"]
        running_max = equity_series.expanding().max()
        drawdown = (equity_series - running_max) / running_max

        return drawdown.min()

    def _calculate_consecutive_wins(self) -> int:
        """Calculate maximum consecutive wins."""
        if self.trades_df.empty:
            return 0

        wins = (self.trades_df["pnl"] > 0).astype(int)
        max_consecutive = 0
        current_consecutive = 0

        for win in wins:
            if win:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_consecutive_losses(self) -> int:
        """Calculate maximum consecutive losses."""
        if self.trades_df.empty:
            return 0

        losses = (self.trades_df["pnl"] < 0).astype(int)
        max_consecutive = 0
        current_consecutive = 0

        for loss in losses:
            if loss:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_drawdown_periods(self, drawdown: pd.Series) -> List[int]:
        """Calculate drawdown periods in days."""
        periods = []
        in_drawdown = False
        start_date = None

        for date, dd in drawdown.items():
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                start_date = date
            elif dd >= 0 and in_drawdown:
                # End of drawdown
                in_drawdown = False
                if start_date:
                    period_days = (date - start_date).days
                    periods.append(period_days)

        # Handle case where drawdown continues to end
        if in_drawdown and start_date:
            period_days = (drawdown.index[-1] - start_date).days
            periods.append(period_days)

        return periods
