# Default configuration for Augment Raptor
# This file contains the base configuration that applies to all environments

# Application settings
app:
  name: "Augment Raptor"
  version: "0.1.0"
  description: "Algorithmic Trading System with Volume Breakout Strategy"

# Data source configuration
data:
  # VNStock API settings
  api:
    base_url: "https://api.vnstock.vn"
    timeout: 30
    rate_limit:
      calls_per_minute: 10
      jitter: 0.1
  
  # Default symbols for scanning
  symbols:
    - "VIC"
    - "VHM" 
    - "VCB"
    - "BID"
    - "CTG"
    - "HPG"
    - "MSN"
    - "TCB"
    - "VRE"
    - "PLX"
  
  # Cache settings
  cache:
    type: "duckdb"  # memory, duckdb
    ttl: 86400  # 24 hours in seconds
    directory: "data/cache"
    cleanup_interval: 3600  # 1 hour

# Strategy configuration
strategy:
  # Volume Breakout strategy parameters
  volume_breakout:
    volume_threshold: 2.0      # Volume must be 2x average
    price_threshold: 0.02      # Price change threshold (2%)
    volume_period: 20          # Volume moving average period
    confirmation_period: 3     # Confirmation period
    stop_loss_atr: 2.0        # Stop loss in ATR units
    take_profit_atr: 4.0      # Take profit in ATR units
    min_volume: 100000        # Minimum volume filter
    trend_filter: true        # Use trend filter

# Backtest configuration
backtest:
  initial_capital: 100000.0
  commission: 0.001          # 0.1% commission
  slippage: 0.001           # 0.1% slippage
  
  # Risk management
  risk:
    max_position_size: 0.1   # Max 10% of capital per position
    risk_per_trade: 0.02     # Risk 2% per trade

# Scanner configuration
scanner:
  default_symbols:
    - "VIC"
    - "VHM"
    - "VCB"
    - "BID"
    - "CTG"
  
  # Scanning parameters
  lookback_days: 90
  min_volume: 100000
  min_price: 10000          # Minimum price in VND

# Logging configuration
logging:
  level: "INFO"
  console_level: "INFO"
  file_logging: true
  log_dir: "logs"
  
  # Log rotation
  max_file_size: "10MB"
  backup_count: 5
  
  # Performance logging
  log_performance: true
  log_trades: true
  log_signals: true

# Database configuration (for future use)
database:
  type: "duckdb"
  path: "data/augment_raptor.db"
  
  # Connection settings
  connection:
    timeout: 30
    pool_size: 5

# Notification settings (for future use)
notifications:
  enabled: false
  
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_address: ""
    to_addresses: []
  
  telegram:
    enabled: false
    bot_token: ""
    chat_id: ""

# Performance settings
performance:
  # Parallel processing
  max_workers: 4
  chunk_size: 100
  
  # Memory management
  memory_limit: "1GB"
  gc_threshold: 0.8

# Security settings
security:
  # API keys (use environment variables in production)
  api_keys: {}
  
  # Rate limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 60

# Feature flags
features:
  enable_caching: true
  enable_parallel_processing: true
  enable_performance_logging: true
  enable_signal_validation: true
