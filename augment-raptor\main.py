#!/usr/bin/env python3
"""
Main entry point for Augment Raptor - Algorithmic Trading System

This script provides the main entry point for running the trading system
either through CLI commands or as a standalone application.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from augment_raptor.cli.commands import cli
from augment_raptor.utils.logger import Logger
from augment_raptor.utils.config import Config


def main():
    """Main entry point for the application."""
    try:
        # Initialize logging
        logger = Logger(__name__)
        logger.info("Starting Augment Raptor")
        
        # Run CLI
        cli()
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
