"""
Helper functions and utilities.

This module contains various utility functions used across the application.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import hashlib
import json


def generate_cache_key(*args, **kwargs) -> str:
    """Generate a cache key from arguments.

    Args:
        *args: Positional arguments
        **kwargs: Keyword arguments

    Returns:
        MD5 hash string as cache key
    """
    # Create a string representation of all arguments
    key_data = {"args": args, "kwargs": sorted(kwargs.items())}

    key_string = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_string.encode()).hexdigest()


def validate_date_format(date_string: str, format_string: str = "%Y-%m-%d") -> bool:
    """Validate date string format.

    Args:
        date_string: Date string to validate
        format_string: Expected date format

    Returns:
        True if valid, False otherwise
    """
    try:
        datetime.strptime(date_string, format_string)
        return True
    except ValueError:
        return False


def get_business_days(start_date: str, end_date: str) -> List[str]:
    """Get list of business days between two dates.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format

    Returns:
        List of business day strings
    """
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)

    business_days = pd.bdate_range(start=start, end=end)
    return [day.strftime("%Y-%m-%d") for day in business_days]


def calculate_returns(prices: pd.Series, method: str = "simple") -> pd.Series:
    """Calculate returns from price series.

    Args:
        prices: Price series
        method: Return calculation method ('simple' or 'log')

    Returns:
        Returns series
    """
    if method == "simple":
        return prices.pct_change()
    elif method == "log":
        return np.log(prices / prices.shift(1))
    else:
        raise ValueError("Method must be 'simple' or 'log'")


def calculate_volatility(returns: pd.Series, window: int = 20, annualize: bool = True) -> pd.Series:
    """Calculate rolling volatility.

    Args:
        returns: Returns series
        window: Rolling window size
        annualize: Whether to annualize volatility

    Returns:
        Volatility series
    """
    vol = returns.rolling(window=window).std()

    if annualize:
        vol = vol * np.sqrt(252)  # Assuming 252 trading days per year

    return vol


def normalize_symbol(symbol: str) -> str:
    """Normalize trading symbol format.

    Args:
        symbol: Raw symbol string

    Returns:
        Normalized symbol string
    """
    return symbol.upper().strip()


def format_currency(amount: float, currency: str = "VND") -> str:
    """Format currency amount for display.

    Args:
        amount: Amount to format
        currency: Currency code

    Returns:
        Formatted currency string
    """
    if currency == "VND":
        return f"{amount:,.0f} VND"
    else:
        return f"{amount:,.2f} {currency}"


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format percentage for display.

    Args:
        value: Percentage value (0.05 = 5%)
        decimals: Number of decimal places

    Returns:
        Formatted percentage string
    """
    return f"{value * 100:.{decimals}f}%"


def safe_divide(
    numerator: Union[float, pd.Series], denominator: Union[float, pd.Series], default: float = 0.0
) -> Union[float, pd.Series]:
    """Safely divide two numbers/series, handling division by zero.

    Args:
        numerator: Numerator value(s)
        denominator: Denominator value(s)
        default: Default value for division by zero

    Returns:
        Division result with safe handling
    """
    if isinstance(denominator, pd.Series):
        return np.where(denominator != 0, numerator / denominator, default)
    else:
        return numerator / denominator if denominator != 0 else default


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split list into chunks of specified size.

    Args:
        lst: List to chunk
        chunk_size: Size of each chunk

    Returns:
        List of chunks
    """
    return [lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: Dict[str, Any], parent_key: str = "", sep: str = ".") -> Dict[str, Any]:
    """Flatten nested dictionary.

    Args:
        d: Dictionary to flatten
        parent_key: Parent key prefix
        sep: Separator for nested keys

    Returns:
        Flattened dictionary
    """
    items = []

    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k

        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))

    return dict(items)


def unflatten_dict(d: Dict[str, Any], sep: str = ".") -> Dict[str, Any]:
    """Unflatten dictionary with dot notation keys.

    Args:
        d: Flattened dictionary
        sep: Separator used in keys

    Returns:
        Nested dictionary
    """
    result = {}

    for key, value in d.items():
        keys = key.split(sep)
        current = result

        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        current[keys[-1]] = value

    return result


def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """Merge multiple dictionaries.

    Args:
        *dicts: Dictionaries to merge

    Returns:
        Merged dictionary
    """
    result = {}

    for d in dicts:
        result.update(d)

    return result


def get_memory_usage(obj: Any) -> str:
    """Get memory usage of an object in human-readable format.

    Args:
        obj: Object to measure

    Returns:
        Memory usage string
    """
    if isinstance(obj, pd.DataFrame):
        memory_bytes = obj.memory_usage(deep=True).sum()
    elif isinstance(obj, pd.Series):
        memory_bytes = obj.memory_usage(deep=True)
    else:
        import sys

        memory_bytes = sys.getsizeof(obj)

    # Convert to human-readable format
    for unit in ["B", "KB", "MB", "GB"]:
        if memory_bytes < 1024.0:
            return f"{memory_bytes:.2f} {unit}"
        memory_bytes /= 1024.0

    return f"{memory_bytes:.2f} TB"


def timing_decorator(func):
    """Decorator to measure function execution time.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """
    import time
    from functools import wraps

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        print(f"{func.__name__} executed in {execution_time:.4f} seconds")

        return result

    return wrapper
