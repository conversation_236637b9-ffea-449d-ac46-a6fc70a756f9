# Contributing to Augment Raptor

Chúng tôi hoan nghênh mọi đóng góp cho Augment Raptor! Tài liệu này sẽ hướng dẫn bạn cách đóng góp hiệu quả cho dự án.

## 🤝 Ways to Contribute

### 1. Bug Reports
- <PERSON><PERSON><PERSON> cáo lỗi qua [GitHub Issues](https://github.com/augment/augment-raptor/issues)
- <PERSON>ung cấp thông tin chi tiết về lỗi
- Bao gồm steps to reproduce
- <PERSON><PERSON>h kèm logs và screenshots nếu có

### 2. Feature Requests
- Đ<PERSON> xuất tính năng mới qua GitHub Issues
- Mô tả rõ ràng use case và benefits
- Thảo luận với community trước khi implement

### 3. Code Contributions
- Fix bugs
- Implement new features
- Improve performance
- Add tests
- Improve documentation

### 4. Documentation
- Cải thiện README và documentation
- Thêm examples và tutorials
- Dịch documentation
- Vi<PERSON>t blog posts và articles

## 🚀 Getting Started

### Prerequisites

- Python 3.9 hoặc cao hơn
- Git
- <PERSON><PERSON><PERSON> thứ<PERSON> c<PERSON> bản về pandas, numpy
- Hi<PERSON><PERSON> biết về thị trường chứng khoán (khuyến khích)

### Development Setup

1. **Fork repository**
   ```bash
   # Fork trên GitHub UI, sau đó clone
   git clone https://github.com/YOUR_USERNAME/augment-raptor.git
   cd augment-raptor
   ```

2. **Setup development environment**
   ```bash
   # Tạo virtual environment
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # hoặc
   venv\Scripts\activate     # Windows

   # Install dependencies
   pip install -r requirements.txt
   pip install -r requirements-dev.txt

   # Install package in development mode
   pip install -e .
   ```

3. **Setup pre-commit hooks**
   ```bash
   pre-commit install
   ```

4. **Verify setup**
   ```bash
   # Run tests
   pytest

   # Run linting
   flake8 src/
   black --check src/
   isort --check-only src/

   # Run CLI
   augment-raptor --help
   ```

## 📝 Development Workflow

### 1. Create Feature Branch

```bash
git checkout -b feature/your-feature-name
# hoặc
git checkout -b bugfix/issue-number
```

### 2. Make Changes

- Viết code theo coding standards
- Thêm tests cho code mới
- Update documentation nếu cần
- Commit thường xuyên với clear messages

### 3. Testing

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=src/augment_raptor

# Run specific test file
pytest tests/test_strategies/test_volume_breakout.py

# Run tests for specific module
pytest tests/test_data/
```

### 4. Code Quality

```bash
# Format code
black src/ tests/
isort src/ tests/

# Check linting
flake8 src/ tests/

# Type checking
mypy src/
```

### 5. Commit Changes

```bash
git add .
git commit -m "feat: add new volume breakout parameter

- Add min_volume parameter to filter low volume stocks
- Update tests and documentation
- Fixes #123"
```

### 6. Push and Create PR

```bash
git push origin feature/your-feature-name
```

Sau đó tạo Pull Request trên GitHub.

## 📋 Coding Standards

### Python Style

- Follow [PEP 8](https://pep8.org/)
- Use [Black](https://black.readthedocs.io/) for formatting
- Use [isort](https://isort.readthedocs.io/) for import sorting
- Maximum line length: 88 characters

### Code Structure

```python
"""Module docstring describing purpose."""

import standard_library
import third_party_library

from augment_raptor.module import local_import


class ExampleClass:
    """Class docstring with clear description.
    
    Args:
        param1: Description of parameter
        param2: Description of parameter
        
    Attributes:
        attr1: Description of attribute
    """
    
    def __init__(self, param1: str, param2: int = 10):
        self.param1 = param1
        self.param2 = param2
    
    def public_method(self, arg: float) -> bool:
        """Method docstring with clear description.
        
        Args:
            arg: Description of argument
            
        Returns:
            Description of return value
            
        Raises:
            ValueError: When arg is invalid
        """
        if arg < 0:
            raise ValueError("arg must be non-negative")
        return True
    
    def _private_method(self) -> None:
        """Private method for internal use."""
        pass
```

### Type Hints

- Sử dụng type hints cho tất cả functions và methods
- Import types từ `typing` module khi cần
- Sử dụng `Optional` cho parameters có thể None

```python
from typing import Optional, List, Dict, Any
import pandas as pd

def process_data(
    data: pd.DataFrame, 
    symbols: List[str], 
    config: Optional[Dict[str, Any]] = None
) -> pd.DataFrame:
    """Process market data with given symbols."""
    pass
```

### Error Handling

```python
import logging

logger = logging.getLogger(__name__)

def fetch_data(symbol: str) -> pd.DataFrame:
    """Fetch data with proper error handling."""
    try:
        # Fetch data logic
        data = api.get_data(symbol)
        if data.empty:
            logger.warning(f"No data found for symbol: {symbol}")
            return pd.DataFrame()
        return data
    except APIError as e:
        logger.error(f"API error fetching {symbol}: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching {symbol}: {e}")
        raise
```

## 🧪 Testing Guidelines

### Test Structure

```python
"""Test module for example functionality."""

import pytest
import pandas as pd
from unittest.mock import Mock, patch

from augment_raptor.module import ExampleClass


class TestExampleClass:
    """Test ExampleClass functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.example = ExampleClass("test", 20)
        
    def test_public_method_valid_input(self):
        """Test public_method with valid input."""
        result = self.example.public_method(5.0)
        assert result is True
        
    def test_public_method_invalid_input(self):
        """Test public_method with invalid input."""
        with pytest.raises(ValueError, match="must be non-negative"):
            self.example.public_method(-1.0)
            
    @patch('augment_raptor.module.external_api')
    def test_with_mocking(self, mock_api):
        """Test with external dependencies mocked."""
        mock_api.get_data.return_value = pd.DataFrame({'price': [100, 101]})
        
        result = self.example.fetch_data("VIC")
        
        assert not result.empty
        mock_api.get_data.assert_called_once_with("VIC")
```

### Test Coverage

- Aim for >80% test coverage
- Test happy paths và edge cases
- Test error conditions
- Mock external dependencies

## 📚 Documentation Guidelines

### Docstrings

Sử dụng Google style docstrings:

```python
def calculate_returns(prices: pd.Series, method: str = "simple") -> pd.Series:
    """Calculate returns from price series.
    
    Args:
        prices: Time series of prices
        method: Calculation method ("simple" or "log")
        
    Returns:
        Time series of returns
        
    Raises:
        ValueError: If method is not supported
        
    Example:
        >>> prices = pd.Series([100, 105, 102])
        >>> returns = calculate_returns(prices)
        >>> print(returns)
        0         NaN
        1    0.050000
        2   -0.028571
        dtype: float64
    """
```

### README Updates

Khi thêm tính năng mới:
- Update feature list
- Add usage examples
- Update installation instructions nếu cần

## 🔄 Pull Request Process

### PR Checklist

- [ ] Code follows style guidelines
- [ ] Tests added for new functionality
- [ ] All tests pass
- [ ] Documentation updated
- [ ] CHANGELOG.md updated (for significant changes)
- [ ] No merge conflicts

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests và quality checks
2. **Code Review**: Maintainers review code for quality và design
3. **Testing**: Manual testing nếu cần
4. **Approval**: Ít nhất 1 maintainer approval
5. **Merge**: Squash and merge vào main branch

## 🐛 Bug Report Template

```markdown
**Bug Description**
Clear description of the bug

**To Reproduce**
Steps to reproduce:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior**
What you expected to happen

**Screenshots**
If applicable, add screenshots

**Environment:**
- OS: [e.g. Windows 10]
- Python version: [e.g. 3.9.7]
- Augment Raptor version: [e.g. 0.1.0]

**Additional Context**
Any other context about the problem
```

## 💡 Feature Request Template

```markdown
**Feature Description**
Clear description of the feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other solutions you've considered

**Additional Context**
Any other context or screenshots
```

## 🏷️ Commit Message Guidelines

### Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples

```bash
feat(strategy): add RSI indicator to volume breakout

- Implement RSI calculation in DataProcessor
- Add RSI threshold parameter to strategy
- Update tests and documentation

Closes #45

fix(cli): handle empty data gracefully

- Add validation for empty DataFrames
- Show user-friendly error messages
- Add tests for edge cases

Fixes #67
```

## 🎯 Development Priorities

### High Priority
- Bug fixes
- Performance improvements
- Security issues
- Documentation improvements

### Medium Priority
- New features
- Code refactoring
- Test coverage improvements

### Low Priority
- Code style improvements
- Minor optimizations
- Nice-to-have features

## 📞 Getting Help

### Community Support
- **GitHub Discussions**: General questions và discussions
- **GitHub Issues**: Bug reports và feature requests
- **Email**: <EMAIL>

### Development Questions
- Check existing issues và discussions
- Ask specific, detailed questions
- Provide context và examples
- Be patient và respectful

## 🙏 Recognition

Contributors sẽ được recognition trong:
- CHANGELOG.md
- Release notes
- Contributors section in README
- GitHub contributors page

Cảm ơn bạn đã quan tâm đến việc đóng góp cho Augment Raptor! 🚀
