#!/usr/bin/env python3
"""
Backtest script for Augment Raptor

This script runs backtests on historical data to evaluate
the performance of trading strategies.
"""

import sys
import os
from pathlib import Path
import argparse
import json

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from augment_raptor.utils.config import Config
from augment_raptor.utils.logger import Logger
from augment_raptor.data.fetcher import VNStockFetcher
from augment_raptor.data.processor import DataProcessor
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
from augment_raptor.backtest.simple_backtest import SimpleBacktest


def main():
    """Main backtest function."""
    parser = argparse.ArgumentParser(description="Augment Raptor Backtest Runner")
    parser.add_argument("--config-dir", default="config", help="Configuration directory")
    parser.add_argument("--environment", default="development", help="Environment")
    parser.add_argument("--symbol", required=True, help="Symbol to backtest")
    parser.add_argument("--start-date", required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument("--initial-capital", type=float, default=100000, help="Initial capital")
    parser.add_argument("--commission", type=float, default=0.001, help="Commission rate")
    parser.add_argument("--output", help="Output file for results")
    parser.add_argument("--plot", action="store_true", help="Generate plots")
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = Config(config_dir=args.config_dir, environment=args.environment)
        
        # Configure logging
        Logger.configure_from_config(config.get_logging_config())
        logger = Logger(__name__)
        
        logger.info(f"Starting backtest for {args.symbol}")
        logger.info(f"Period: {args.start_date} to {args.end_date}")
        logger.info(f"Initial capital: {args.initial_capital:,.2f}")
        
        # Initialize components
        fetcher = VNStockFetcher()
        processor = DataProcessor()
        
        # Initialize strategy
        strategy_params = config.get('strategy.volume_breakout', {})
        strategy = VolumeBreakoutStrategy(strategy_params)
        
        # Initialize backtest engine
        backtest_config = config.get_backtest_config()
        backtest_engine = SimpleBacktest(
            initial_capital=args.initial_capital,
            commission=args.commission,
            slippage=backtest_config.get('slippage', 0.001)
        )
        
        # Fetch and process data
        logger.info("Fetching data...")
        data = fetcher.fetch(args.symbol, args.start_date, args.end_date)
        
        if data.empty:
            logger.error(f"No data available for {args.symbol}")
            sys.exit(1)
        
        logger.info(f"Fetched {len(data)} data points")
        
        # Process data
        logger.info("Processing data...")
        processed_data = processor.process(data)
        
        # Run backtest
        logger.info("Running backtest...")
        results = backtest_engine.run(strategy, processed_data)
        
        if 'error' in results:
            logger.error(f"Backtest failed: {results['error']}")
            sys.exit(1)
        
        # Display results
        print(f"\nBacktest Results for {args.symbol}")
        print("=" * 60)
        print(f"Strategy: {results['strategy']}")
        print(f"Period: {results['start_date']} to {results['end_date']}")
        print(f"Initial Capital: {results['initial_capital']:,.2f}")
        print(f"Final Capital: {results['final_capital']:,.2f}")
        print(f"Total Return: {results['total_return'] * 100:.2f}%")
        print(f"Total Trades: {results['total_trades']}")
        
        # Performance metrics
        metrics = results.get('metrics', {})
        if metrics:
            print("\nPerformance Metrics:")
            print(f"  Win Rate: {metrics.get('win_rate_pct', 0):.2f}%")
            print(f"  Profit Factor: {metrics.get('profit_factor', 0):.2f}")
            print(f"  Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"  Sortino Ratio: {metrics.get('sortino_ratio', 0):.2f}")
            print(f"  Max Drawdown: {metrics.get('max_drawdown_pct', 0):.2f}%")
            print(f"  Average Win: {metrics.get('avg_win', 0):.2f}")
            print(f"  Average Loss: {metrics.get('avg_loss', 0):.2f}")
            print(f"  Largest Win: {metrics.get('largest_win', 0):.2f}")
            print(f"  Largest Loss: {metrics.get('largest_loss', 0):.2f}")
            print(f"  Avg Holding Days: {metrics.get('avg_holding_days', 0):.1f}")
        
        # Trade summary
        trades = results.get('trades', [])
        if trades:
            winning_trades = [t for t in trades if t['pnl'] > 0]
            losing_trades = [t for t in trades if t['pnl'] < 0]
            
            print(f"\nTrade Summary:")
            print(f"  Total Trades: {len(trades)}")
            print(f"  Winning Trades: {len(winning_trades)}")
            print(f"  Losing Trades: {len(losing_trades)}")
            
            if winning_trades:
                avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades)
                print(f"  Average Win: {avg_win:.2f}")
            
            if losing_trades:
                avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades)
                print(f"  Average Loss: {avg_loss:.2f}")
        
        # Save results if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Results saved to {args.output}")
        
        # Generate plots if requested
        if args.plot:
            try:
                import matplotlib.pyplot as plt
                import pandas as pd
                
                # Extract equity curve
                equity_curve = results.get('equity_curve', [])
                if equity_curve:
                    df = pd.DataFrame(equity_curve)
                    df['date'] = pd.to_datetime(df['date'])
                    
                    # Create plots
                    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
                    
                    # Equity curve
                    ax1.plot(df['date'], df['equity'])
                    ax1.set_title(f'Equity Curve - {args.symbol}')
                    ax1.set_ylabel('Equity')
                    ax1.grid(True)
                    
                    # Price chart with positions
                    ax2.plot(df['date'], df['price'], label='Price')
                    
                    # Mark positions
                    long_positions = df[df['position'] > 0]
                    short_positions = df[df['position'] < 0]
                    
                    if not long_positions.empty:
                        ax2.scatter(long_positions['date'], long_positions['price'], 
                                  color='green', marker='^', s=50, label='Long Position')
                    
                    if not short_positions.empty:
                        ax2.scatter(short_positions['date'], short_positions['price'], 
                                  color='red', marker='v', s=50, label='Short Position')
                    
                    ax2.set_title(f'Price Chart with Positions - {args.symbol}')
                    ax2.set_ylabel('Price')
                    ax2.set_xlabel('Date')
                    ax2.legend()
                    ax2.grid(True)
                    
                    plt.tight_layout()
                    
                    # Save plot
                    plot_file = f"backtest_{args.symbol}_{args.start_date}_{args.end_date}.png"
                    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
                    print(f"\nPlot saved to {plot_file}")
                    
            except ImportError:
                logger.warning("Matplotlib not available for plotting")
            except Exception as e:
                logger.error(f"Error generating plots: {str(e)}")
        
        logger.info("Backtest completed successfully")
        
    except Exception as e:
        logger.error(f"Backtest failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
