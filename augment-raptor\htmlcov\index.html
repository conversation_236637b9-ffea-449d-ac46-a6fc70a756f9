<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32.png">
    <link rel="stylesheet" href="style.css" type="text/css">
    <script type="text/javascript" src="coverage_html.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">62%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed.png" alt="Show/hide keyboard shortcuts" />
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter..." />
        </form>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.2.7">coverage.py v7.2.7</a>,
            created at 2025-07-02 17:20 +0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th class="name left" aria-sort="none" data-shortcut="n">Module</th>
                <th aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements</th>
                <th aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing</th>
                <th aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded</th>
                <th class="right" aria-sort="none" data-shortcut="c">coverage</th>
            </tr>
        </thead>
        <tbody>
            <tr class="file">
                <td class="name left"><a href="d_66e1908eb64c8e84___init___py.html">src\augment_raptor\__init__.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_123cd40925cbbb75___init___py.html">src\augment_raptor\backtest\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_123cd40925cbbb75_metrics_py.html">src\augment_raptor\backtest\metrics.py</a></td>
                <td>139</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="120 139">86%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_123cd40925cbbb75_simple_backtest_py.html">src\augment_raptor\backtest\simple_backtest.py</a></td>
                <td>126</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="102 126">81%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_ffaebc813917a072___init___py.html">src\augment_raptor\cli\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_ffaebc813917a072_commands_py.html">src\augment_raptor\cli\commands.py</a></td>
                <td>185</td>
                <td>81</td>
                <td>2</td>
                <td class="right" data-ratio="104 185">56%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_ffaebc813917a072_commands_old_py.html">src\augment_raptor\cli\commands_old.py</a></td>
                <td>185</td>
                <td>185</td>
                <td>2</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_79c74c2446df8b69___init___py.html">src\augment_raptor\data\__init__.py</a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_79c74c2446df8b69_fetcher_py.html">src\augment_raptor\data\fetcher.py</a></td>
                <td>63</td>
                <td>9</td>
                <td>8</td>
                <td class="right" data-ratio="54 63">86%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_79c74c2446df8b69_processor_py.html">src\augment_raptor\data\processor.py</a></td>
                <td>61</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="60 61">98%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_79c74c2446df8b69_storage_py.html">src\augment_raptor\data\storage.py</a></td>
                <td>107</td>
                <td>61</td>
                <td>16</td>
                <td class="right" data-ratio="46 107">43%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_8551d8f34bd0b620___init___py.html">src\augment_raptor\strategies\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_8551d8f34bd0b620_base_py.html">src\augment_raptor\strategies\base.py</a></td>
                <td>41</td>
                <td>14</td>
                <td>16</td>
                <td class="right" data-ratio="27 41">66%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_8551d8f34bd0b620_volume_breakout_py.html">src\augment_raptor\strategies\volume_breakout.py</a></td>
                <td>87</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="74 87">85%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_8551d8f34bd0b620_volume_breakout_old_py.html">src\augment_raptor\strategies\volume_breakout_old.py</a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_f345f19362c68e46___init___py.html">src\augment_raptor\utils\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_f345f19362c68e46_config_py.html">src\augment_raptor\utils\config.py</a></td>
                <td>102</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="90 102">88%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_f345f19362c68e46_helpers_py.html">src\augment_raptor\utils\helpers.py</a></td>
                <td>93</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="87 93">94%</td>
            </tr>
            <tr class="file">
                <td class="name left"><a href="d_f345f19362c68e46_logger_py.html">src\augment_raptor\utils\logger.py</a></td>
                <td>97</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="85 97">88%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1401</td>
                <td>526</td>
                <td>44</td>
                <td class="right" data-ratio="875 1401">62%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.2.7">coverage.py v7.2.7</a>,
            created at 2025-07-02 17:20 +0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="d_f345f19362c68e46_logger_py.html"/>
        <a id="nextFileLink" class="nav" href="d_66e1908eb64c8e84___init___py.html"/>
        <button type="button" class="button_prev_file" data-shortcut="["/>
        <button type="button" class="button_next_file" data-shortcut="]"/>
        <button type="button" class="button_show_hide_help" data-shortcut="?"/>
    </aside>
</footer>
</body>
</html>
