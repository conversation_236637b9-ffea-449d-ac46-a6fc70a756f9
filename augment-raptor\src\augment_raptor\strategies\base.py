"""
Base strategy interface for trading strategies.

This module defines the abstract Strategy class that all trading
strategies must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import pandas as pd
from ..utils.logger import Logger


class Strategy(ABC):
    """Abstract base class for trading strategies."""

    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        """Initialize strategy with name and parameters.

        Args:
            name: Strategy name
            parameters: Strategy parameters dictionary
        """
        self.name = name
        self.parameters = parameters or {}
        self.logger = Logger(f"{__name__}.{name}")

    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data and return analysis results.

        Args:
            data: DataFrame containing OHLCV data with technical indicators

        Returns:
            Dictionary containing analysis results including:
            - signals: Trading signals
            - metrics: Strategy-specific metrics
            - metadata: Additional information
        """
        pass

    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on data.

        Args:
            data: DataFrame containing OHLCV data

        Returns:
            DataFrame with added signal columns:
            - signal: 1 for buy, -1 for sell, 0 for hold
            - signal_strength: Signal strength (0-1)
            - entry_price: Suggested entry price
            - stop_loss: Suggested stop loss price
            - take_profit: Suggested take profit price
        """
        pass

    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters.

        Returns:
            Dictionary of parameter names and values
        """
        pass

    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """Set strategy parameters.

        Args:
            parameters: Dictionary of parameter names and values
        """
        pass

    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data format.

        Args:
            data: DataFrame to validate

        Returns:
            True if data is valid, False otherwise
        """
        required_columns = ["Open", "High", "Low", "Close", "Volume"]

        if data.empty:
            self.logger.warning("Empty dataframe provided")
            return False

        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.error(f"Missing required columns: {missing_columns}")
            return False

        # Check for sufficient data
        if len(data) < self.get_minimum_data_length():
            self.logger.warning(
                f"Insufficient data: {len(data)} rows, "
                f"need at least {self.get_minimum_data_length()}"
            )
            return False

        return True

    def get_minimum_data_length(self) -> int:
        """Get minimum number of data points required for strategy.

        Returns:
            Minimum number of data points
        """
        return 50  # Default minimum

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information.

        Returns:
            Dictionary containing strategy metadata
        """
        return {
            "name": self.name,
            "parameters": self.parameters,
            "minimum_data_length": self.get_minimum_data_length(),
            "description": self.__doc__ or "No description available",
        }

    def backtest_compatible(self) -> bool:
        """Check if strategy is compatible with backtesting.

        Returns:
            True if strategy can be backtested
        """
        return True

    def get_required_indicators(self) -> List[str]:
        """Get list of required technical indicators.

        Returns:
            List of indicator names required by this strategy
        """
        return []  # Override in subclasses

    def calculate_position_size(
        self, data: pd.DataFrame, account_balance: float, risk_per_trade: float = 0.02
    ) -> float:
        """Calculate position size based on risk management.

        Args:
            data: Current market data
            account_balance: Available account balance
            risk_per_trade: Risk per trade as percentage of balance

        Returns:
            Position size in currency units
        """
        if data.empty:
            return 0.0

        # Simple position sizing based on ATR
        current_price = data["Close"].iloc[-1]
        atr = data.get("ATR_14", pd.Series([current_price * 0.02])).iloc[-1]

        # Risk amount
        risk_amount = account_balance * risk_per_trade

        # Position size based on ATR stop loss
        stop_loss_distance = atr * 2  # 2 ATR stop loss

        if stop_loss_distance > 0:
            position_size = risk_amount / stop_loss_distance
            return min(position_size, account_balance * 0.1)  # Max 10% of balance

        return 0.0
