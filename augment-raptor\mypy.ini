[mypy]
python_version = 3.9
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = false
warn_redundant_casts = true
warn_unused_ignores = false
warn_no_return = true
warn_unreachable = false
strict_equality = true
ignore_missing_imports = true
# Disable strict variable annotation requirements
disallow_any_unimported = false
disallow_any_expr = false
disallow_any_decorated = false
disallow_any_explicit = false
disallow_any_generics = false
disallow_subclassing_any = false
# Disable variable annotation requirements
disable_error_code = var-annotated

# Ignore specific modules that are hard to type
[mypy-yaml.*]
ignore_missing_imports = true

[mypy-requests.*]
ignore_missing_imports = true

[mypy-duckdb.*]
ignore_missing_imports = true

[mypy-pandas.*]
ignore_missing_imports = true

[mypy-numpy.*]
ignore_missing_imports = true

[mypy-matplotlib.*]
ignore_missing_imports = true

[mypy-plotly.*]
ignore_missing_imports = true

[mypy-click.*]
ignore_missing_imports = true

# Relax rules for specific files
[mypy-augment_raptor.cli.*]
disallow_untyped_defs = false
disallow_incomplete_defs = false

[mypy-augment_raptor.utils.helpers]
disallow_untyped_defs = false

[mypy-augment_raptor.utils.config]
disallow_untyped_defs = false

[mypy-augment_raptor.backtest.simple_backtest]
disallow_untyped_defs = false
ignore_errors = true

[mypy-tests.*]
ignore_errors = true
