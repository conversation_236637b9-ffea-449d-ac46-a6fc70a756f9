"""
Tests for CLI commands module.
"""

import tempfile
import os
import yaml
from unittest.mock import Mock, patch, MagicMock
from click.testing import C<PERSON><PERSON><PERSON>ner
import pandas as pd
from datetime import datetime

from augment_raptor.cli.commands import cli


class TestCLICommands:
    """Test CLI commands functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

        # Create temporary config directory
        self.temp_dir = tempfile.mkdtemp()

        # Create test config files
        self.default_config = {
            'app': {'name': 'Augment Raptor', 'version': '1.0.0'},
            'logging': {'level': 'INFO'},
            'scanner': {'default_symbols': ['VIC', 'VCB']}
        }

        with open(os.path.join(self.temp_dir, 'default.yaml'), 'w') as f:
            yaml.dump(self.default_config, f)

    def teardown_method(self):
        """Clean up test fixtures."""
        # Remove temporary files
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_cli_group_basic(self):
        """Test basic CLI group functionality."""
        result = self.runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        assert "Augment Raptor" in result.output

    def test_cli_group_with_config_dir(self):
        """Test CLI group with custom config directory."""
        result = self.runner.invoke(cli, ['--config-dir', self.temp_dir, '--help'])
        assert result.exit_code == 0
        assert "Augment Raptor" in result.output

    def test_cli_group_verbose(self):
        """Test CLI group with verbose flag."""
        result = self.runner.invoke(cli, ['--config-dir', self.temp_dir, '--verbose', '--help'])
        assert result.exit_code == 0
        assert "Augment Raptor" in result.output

    def test_scan_command_help(self):
        """Test scan command help."""
        result = self.runner.invoke(cli, ['scan', '--help'])
        assert result.exit_code == 0
        assert "scan" in result.output.lower()
        assert "symbols" in result.output.lower()

    def test_backtest_command_help(self):
        """Test backtest command help."""
        result = self.runner.invoke(cli, ['backtest', '--help'])
        assert result.exit_code == 0
        assert "backtest" in result.output.lower()
        assert "symbol" in result.output.lower()

    def test_update_data_command_help(self):
        """Test update-data command help."""
        result = self.runner.invoke(cli, ['update-data', '--help'])
        assert result.exit_code == 0
        assert "update" in result.output.lower()
        assert "symbol" in result.output.lower()


class TestCLICommandsWithMocking:
    """Test CLI commands with mocked dependencies."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

        # Create temporary config directory
        self.temp_dir = tempfile.mkdtemp()

        # Create test config files
        self.default_config = {
            'app': {'name': 'Augment Raptor', 'version': '1.0.0'},
            'logging': {'level': 'INFO'},
            'scanner': {'default_symbols': ['VIC', 'VCB']}
        }

        with open(os.path.join(self.temp_dir, 'default.yaml'), 'w') as f:
            yaml.dump(self.default_config, f)

    def teardown_method(self):
        """Clean up test fixtures."""
        # Remove temporary files
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    @patch('augment_raptor.cli.commands.VNStockFetcher')
    @patch('augment_raptor.cli.commands.DataProcessor')
    @patch('augment_raptor.cli.commands.VolumeBreakoutStrategy')
    def test_scan_command_with_mocks(self, mock_strategy, mock_processor, mock_fetcher):
        """Test scan command with mocked dependencies."""
        # Setup mocks
        mock_fetcher_instance = Mock()
        mock_fetcher.return_value = mock_fetcher_instance

        mock_processor_instance = Mock()
        mock_processor.return_value = mock_processor_instance

        mock_strategy_instance = Mock()
        mock_strategy_instance.generate_signals.return_value = pd.DataFrame({
            'symbol': ['VIC'],
            'signal': [1],
            'strength': [0.8],
            'timestamp': [datetime.now()]
        })
        mock_strategy.return_value = mock_strategy_instance

        # Mock data
        mock_data = pd.DataFrame({
            'close': [100, 105, 110],
            'volume': [1000, 1500, 2000],
            'high': [102, 107, 112],
            'low': [98, 103, 108]
        })
        mock_fetcher_instance.get_stock_data.return_value = mock_data
        mock_processor_instance.add_technical_indicators.return_value = mock_data

        # Test scan command
        result = self.runner.invoke(cli, [
            '--config-dir', self.temp_dir,
            'scan',
            '--symbols', 'VIC',
            '--start-date', '2023-01-01',
            '--end-date', '2023-01-31'
        ])

        # Should not crash (exit code 0 or 1 is acceptable for CLI commands)
        assert result.exit_code in [0, 1]

        # Verify mocks were called
        mock_fetcher.assert_called_once()
        mock_processor.assert_called_once()
        mock_strategy.assert_called_once()

    @patch('augment_raptor.cli.commands.VNStockFetcher')
    @patch('augment_raptor.cli.commands.DataProcessor')
    @patch('augment_raptor.cli.commands.VolumeBreakoutStrategy')
    @patch('augment_raptor.cli.commands.SimpleBacktest')
    def test_backtest_command_with_mocks(self, mock_backtest, mock_strategy, mock_processor, mock_fetcher):
        """Test backtest command with mocked dependencies."""
        # Setup mocks
        mock_fetcher_instance = Mock()
        mock_fetcher.return_value = mock_fetcher_instance

        mock_processor_instance = Mock()
        mock_processor.return_value = mock_processor_instance

        mock_strategy_instance = Mock()
        mock_strategy.return_value = mock_strategy_instance

        mock_backtest_instance = Mock()
        mock_backtest_instance.run.return_value = {
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': -0.05,
            'num_trades': 10
        }
        mock_backtest.return_value = mock_backtest_instance

        # Mock data
        mock_data = pd.DataFrame({
            'close': [100, 105, 110],
            'volume': [1000, 1500, 2000],
            'high': [102, 107, 112],
            'low': [98, 103, 108]
        })
        mock_fetcher_instance.get_stock_data.return_value = mock_data
        mock_processor_instance.add_technical_indicators.return_value = mock_data

        # Test backtest command
        result = self.runner.invoke(cli, [
            '--config-dir', self.temp_dir,
            'backtest',
            '--symbol', 'VIC',
            '--start-date', '2023-01-01',
            '--end-date', '2023-01-31'
        ])

        # Should not crash
        assert result.exit_code in [0, 1]

        # Verify mocks were called
        mock_fetcher.assert_called_once()
        mock_processor.assert_called_once()
        mock_strategy.assert_called_once()
        mock_backtest.assert_called_once()

    @patch('augment_raptor.cli.commands.VNStockFetcher')
    def test_update_data_command_with_mocks(self, mock_fetcher):
        """Test update-data command with mocked dependencies."""
        # Setup mocks
        mock_fetcher_instance = Mock()
        mock_fetcher_instance.update_cache.return_value = None
        mock_fetcher.return_value = mock_fetcher_instance

        # Test update-data command
        result = self.runner.invoke(cli, [
            '--config-dir', self.temp_dir,
            'update-data',
            '--symbol', 'VIC'
        ])

        # Should not crash
        assert result.exit_code in [0, 1]

        # Verify mock was called
        mock_fetcher.assert_called_once()


class TestCLIErrorHandling:
    """Test CLI error handling scenarios."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    def test_cli_with_invalid_config_dir(self):
        """Test CLI with invalid config directory."""
        result = self.runner.invoke(cli, ['--config-dir', '/nonexistent/path', '--help'])
        # Should still show help
        assert result.exit_code == 0
        assert "Augment Raptor" in result.output

    def test_scan_with_invalid_date_format(self):
        """Test scan command with invalid date format."""
        result = self.runner.invoke(cli, [
            'scan',
            '--start-date', 'invalid-date',
            '--symbols', 'VIC'
        ])
        # Should handle error gracefully
        assert result.exit_code in [0, 1, 2]  # Various exit codes are acceptable

    def test_backtest_with_missing_symbol(self):
        """Test backtest command without required symbol."""
        result = self.runner.invoke(cli, ['backtest'])
        # Should show error or help
        assert result.exit_code in [0, 1, 2]

