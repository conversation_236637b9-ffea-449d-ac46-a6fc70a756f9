# Development Guide - Augment Raptor

Hướng dẫn phát triển cho dự án Augment Raptor.

## 🛠️ Setup Development Environment

### 1. Clone và Setup

```bash
# Clone repository
git clone https://github.com/augment/augment-raptor.git
cd augment-raptor

# Tạo virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows

# Cài đặt dependencies
pip install -r requirements-dev.txt

# Cài đặt package ở chế độ development
pip install -e .
```

### 2. Pre-commit Hooks

```bash
# Cài đặt pre-commit hooks
pre-commit install

# Chạy pre-commit trên tất cả files
pre-commit run --all-files
```

### 3. Configuration

Copy và chỉnh sửa file cấu hình development:

```bash
cp config/development.yaml config/local.yaml
# Chỉnh sửa config/local.yaml theo nhu cầu
```

## 🧪 Testing

### Chạy Tests

```bash
# Chạy tất cả tests
pytest

# Chạy tests với coverage
pytest --cov=src/augment_raptor --cov-report=html

# Chạy tests cụ thể
pytest tests/test_strategies/test_volume_breakout.py

# Chạy tests với markers
pytest -m "not slow"  # Bỏ qua slow tests
pytest -m "integration"  # Chỉ chạy integration tests
```

### Viết Tests

```python
# tests/test_example.py
import pytest
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy

def test_volume_breakout_initialization():
    """Test strategy initialization."""
    strategy = VolumeBreakoutStrategy()
    assert strategy.name == "Volume Breakout"
    assert strategy.parameters['volume_threshold'] == 2.0

@pytest.mark.slow
def test_backtest_performance():
    """Test backtest performance (slow test)."""
    # Implementation here
    pass
```

## 🔍 Code Quality

### Linting và Formatting

```bash
# Format code với Black
black src/ tests/

# Sort imports với isort
isort src/ tests/

# Lint với flake8
flake8 src/ tests/

# Type checking với mypy
mypy src/
```

### Code Style Guidelines

- Sử dụng **Black** cho formatting (line length: 100)
- Sử dụng **Google docstring style**
- Type hints cho tất cả public functions
- Tên biến và function sử dụng snake_case
- Tên class sử dụng PascalCase

### Example Code Style

```python
from typing import Dict, List, Optional
import pandas as pd

class ExampleStrategy:
    """Example strategy implementation.
    
    This class demonstrates the coding style and documentation
    standards for the Augment Raptor project.
    
    Args:
        parameters: Strategy parameters dictionary
        
    Attributes:
        name: Strategy name
        parameters: Strategy parameters
    """
    
    def __init__(self, parameters: Optional[Dict[str, Any]] = None) -> None:
        """Initialize strategy with parameters."""
        self.name = "Example Strategy"
        self.parameters = parameters or {}
    
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market data and return results.
        
        Args:
            data: Market data with OHLCV columns
            
        Returns:
            Dictionary containing analysis results
            
        Raises:
            ValueError: If data is invalid
        """
        if data.empty:
            raise ValueError("Data cannot be empty")
            
        # Implementation here
        return {"signals": 0, "strength": 0.0}
```

## 📁 Project Structure

```
augment-raptor/
├── src/augment_raptor/          # Source code
│   ├── data/                    # Data handling modules
│   ├── strategies/              # Trading strategies
│   ├── backtest/               # Backtesting engine
│   ├── cli/                    # Command line interface
│   └── utils/                  # Utility functions
├── tests/                      # Test files
├── config/                     # Configuration files
├── scripts/                    # Standalone scripts
├── docs/                       # Documentation
├── data/                       # Data storage (gitignored)
└── logs/                       # Log files (gitignored)
```

## 🔧 Adding New Features

### 1. Adding a New Strategy

```python
# src/augment_raptor/strategies/my_strategy.py
from .base import Strategy

class MyStrategy(Strategy):
    """My custom trading strategy."""
    
    def __init__(self, parameters=None):
        super().__init__("My Strategy", parameters)
    
    def analyze(self, data):
        # Implementation
        pass
    
    def generate_signals(self, data):
        # Implementation
        pass
```

### 2. Adding Tests

```python
# tests/test_strategies/test_my_strategy.py
import pytest
from augment_raptor.strategies.my_strategy import MyStrategy

class TestMyStrategy:
    """Test cases for MyStrategy."""
    
    def test_initialization(self):
        strategy = MyStrategy()
        assert strategy.name == "My Strategy"
    
    def test_analyze(self):
        # Test implementation
        pass
```

### 3. Adding Configuration

```yaml
# config/default.yaml
strategy:
  my_strategy:
    parameter1: value1
    parameter2: value2
```

## 🐛 Debugging

### Logging

```python
from augment_raptor.utils.logger import Logger

logger = Logger(__name__)

# Different log levels
logger.debug("Debug information")
logger.info("General information")
logger.warning("Warning message")
logger.error("Error occurred")
logger.critical("Critical error")

# Structured logging
logger.info("Trade executed", symbol="VIC", price=100000, quantity=100)
```

### Performance Profiling

```python
from augment_raptor.utils.helpers import timing_decorator

@timing_decorator
def slow_function():
    # Function implementation
    pass

# Or use context manager
import time
with logger.performance_context("data_processing"):
    # Code to profile
    time.sleep(1)
```

## 📊 Performance Guidelines

### Data Processing

- Sử dụng vectorized operations với pandas/numpy
- Tránh loops khi có thể
- Sử dụng caching cho expensive operations
- Lazy loading cho large datasets

### Memory Management

- Sử dụng generators cho large data streams
- Clean up resources trong finally blocks
- Monitor memory usage với memory-profiler

### Example Optimization

```python
# ❌ Slow - using loops
def calculate_signals_slow(data):
    signals = []
    for i in range(len(data)):
        if data.iloc[i]['volume'] > data.iloc[i]['avg_volume'] * 2:
            signals.append(1)
        else:
            signals.append(0)
    return signals

# ✅ Fast - vectorized
def calculate_signals_fast(data):
    return (data['volume'] > data['avg_volume'] * 2).astype(int)
```

## 🚀 Release Process

### 1. Version Bumping

```bash
# Update version in src/augment_raptor/__init__.py
__version__ = "0.2.0"

# Create git tag
git tag v0.2.0
git push origin v0.2.0
```

### 2. Building Package

```bash
# Build package
python -m build

# Check package
twine check dist/*

# Upload to PyPI (test)
twine upload --repository testpypi dist/*

# Upload to PyPI (production)
twine upload dist/*
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/my-feature`
3. Make changes và add tests
4. Run tests và quality checks
5. Commit: `git commit -m "Add my feature"`
6. Push: `git push origin feature/my-feature`
7. Create Pull Request

### Pull Request Checklist

- [ ] Tests pass
- [ ] Code coverage maintained
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Pre-commit hooks pass
- [ ] No breaking changes (or documented)

## 📚 Resources

- [Python Style Guide](https://pep8.org/)
- [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html)
- [Pandas Documentation](https://pandas.pydata.org/docs/)
- [pytest Documentation](https://docs.pytest.org/)
- [Black Documentation](https://black.readthedocs.io/)
