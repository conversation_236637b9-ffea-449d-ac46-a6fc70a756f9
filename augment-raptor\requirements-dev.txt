# Development dependencies for Augment Raptor

# Include production requirements
-r requirements.txt

# Testing frameworks
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.8.0
pytest-asyncio>=0.21.0
pytest-benchmark>=4.0.0

# Code quality and linting
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0
mypy>=0.991

# Pre-commit hooks
pre-commit>=2.20.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
myst-parser>=0.18.0

# Development tools
ipython>=8.0.0
jupyter>=1.0.0
notebook>=6.4.0

# Debugging
pdb++>=0.10.0
ipdb>=0.13.0

# Performance profiling
line-profiler>=4.0.0
py-spy>=0.3.0

# Security scanning
bandit>=1.7.0
safety>=2.0.0

# Type checking
types-PyYAML>=6.0.0
types-requests>=2.28.0

# Code coverage
coverage>=6.0.0

# Build tools
build>=0.8.0
twine>=4.0.0

# Environment management
python-dotenv>=0.20.0

# API documentation
fastapi>=0.85.0  # For future API development
uvicorn>=0.18.0

# Data visualization for development
seaborn>=0.11.0
bokeh>=2.4.0

# Development utilities
rich>=12.0.0  # Better console output
typer>=0.6.0  # Alternative CLI framework
