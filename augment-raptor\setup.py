"""
Setup script for Augment Raptor - Algorithmic Trading System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements(filename):
    with open(filename, "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# Get version from package
def get_version():
    version_file = os.path.join("src", "augment_raptor", "__init__.py")
    with open(version_file, "r", encoding="utf-8") as fh:
        for line in fh:
            if line.startswith("__version__"):
                return line.split("=")[1].strip().strip('"').strip("'")
    return "0.1.0"

setup(
    name="augment-raptor",
    version=get_version(),
    author="Augment Team",
    author_email="<EMAIL>",
    description="Algorithmic Trading System with Volume Breakout Strategy",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/augment/augment-raptor",
    
    # Package configuration
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    
    # Dependencies
    install_requires=read_requirements("requirements.txt"),
    extras_require={
        "dev": read_requirements("requirements-dev.txt"),
    },
    
    # Entry points
    entry_points={
        "console_scripts": [
            "augment-raptor=augment_raptor.cli.commands:cli",
            "raptor=augment_raptor.cli.commands:cli",
        ],
    },
    
    # Classifiers
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    
    # Python version requirement
    python_requires=">=3.9",
    
    # Include additional files
    include_package_data=True,
    package_data={
        "augment_raptor": [
            "config/*.yaml",
            "data/*.sql",
        ],
    },
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/augment/augment-raptor/issues",
        "Source": "https://github.com/augment/augment-raptor",
        "Documentation": "https://augment-raptor.readthedocs.io/",
    },
    
    # Keywords
    keywords="trading, algorithmic, finance, stock, vietnam, volume, breakout",
    
    # Zip safe
    zip_safe=False,
)
