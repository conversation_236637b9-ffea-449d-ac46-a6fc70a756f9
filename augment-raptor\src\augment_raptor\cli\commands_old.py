"""
Command-line interface commands for Augment Raptor.

This module implements CLI commands for scanning markets,
running backtests, and other trading operations.
"""

import click
import pandas as pd
from typing import List, Optional
from datetime import datetime, timedelta
import json

from ..utils.config import Config
from ..utils.logger import Logger
from ..data.fetcher import VNStockFetcher
from ..data.processor import DataProcessor
from ..strategies.volume_breakout import VolumeBreakoutStrategy
from ..backtest.simple_backtest import SimpleBacktest


@click.group()
@click.option("--config-dir", default="config", help="Configuration directory")
@click.option("--environment", default="development", help="Environment name")
@click.option("--verbose", "-v", is_flag=True, help="Verbose output")
@click.pass_context
def cli(ctx, config_dir, environment, verbose):
    """Augment Raptor - Algorithmic Trading System"""
    # Initialize context
    ctx.ensure_object(dict)

    # Load configuration
    config = Config(config_dir=config_dir, environment=environment)
    ctx.obj["config"] = config

    # Configure logging
    log_level = "DEBUG" if verbose else config.get("logging.level", "INFO")
    Logger.configure_from_config({"level": log_level})

    ctx.obj["logger"] = Logger(__name__)
    ctx.obj["logger"].info(f"Augment Raptor CLI started - Environment: {environment}")


@cli.command()
@click.option("--symbols", "-s", multiple=True, help="Stock symbols to scan")
@click.option("--start-date", help="Start date (YYYY-MM-DD)")
@click.option("--end-date", help="End date (YYYY-MM-DD)")
@click.option("--strategy", default="volume_breakout", help="Strategy to use")
@click.option("--output", "-o", help="Output file for results")
@click.option(
    "--format",
    "output_format",
    default="table",
    type=click.Choice(["table", "json", "csv"]),
    help="Output format",
)
@click.pass_context
def scan(ctx, symbols, start_date, end_date, strategy, output, output_format):
    """Scan market for trading opportunities."""
    config = ctx.obj["config"]
    logger = ctx.obj["logger"]

    try:
        # Default date range if not provided
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if not start_date:
            start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")

        # Default symbols if not provided
        if not symbols:
            symbols = config.get("scanner.default_symbols", ["VIC", "VHM", "VCB", "BID", "CTG"])

        logger.info(f"Scanning {len(symbols)} symbols from {start_date} to {end_date}")

        # Initialize components
        fetcher = VNStockFetcher()
        processor = DataProcessor()

        # Initialize strategy
        if strategy == "volume_breakout":
            strategy_params = config.get("strategy.volume_breakout", {})
            trading_strategy = VolumeBreakoutStrategy(strategy_params)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")

        # Scan symbols
        results = []

        with click.progressbar(symbols, label="Scanning symbols") as symbol_list:
            for symbol in symbol_list:
                try:
                    # Fetch data
                    data = fetcher.fetch(symbol, start_date, end_date)

                    if data.empty:
                        logger.warning(f"No data for {symbol}")
                        continue

                    # Process data
                    processed_data = processor.process(data)

                    # Analyze with strategy
                    analysis = trading_strategy.analyze(processed_data)

                    if "error" not in analysis:
                        analysis["symbol"] = symbol
                        results.append(analysis)

                except Exception as e:
                    logger.error(f"Error scanning {symbol}: {str(e)}")

        # Display results
        if results:
            _display_scan_results(results, output_format, output)
            logger.info(f"Scan completed: {len(results)} symbols analyzed")
        else:
            click.echo("No results found")

    except Exception as e:
        logger.error(f"Scan failed: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
@click.option("--symbol", "-s", required=True, help="Stock symbol to backtest")
@click.option("--start-date", required=True, help="Start date (YYYY-MM-DD)")
@click.option("--end-date", required=True, help="End date (YYYY-MM-DD)")
@click.option("--strategy", default="volume_breakout", help="Strategy to backtest")
@click.option("--initial-capital", default=100000, type=float, help="Initial capital")
@click.option("--commission", default=0.001, type=float, help="Commission rate")
@click.option("--output", "-o", help="Output file for results")
@click.option("--plot", is_flag=True, help="Generate performance plots")
@click.pass_context
def backtest(
    ctx, symbol, start_date, end_date, strategy, initial_capital, commission, output, plot
):
    """Run backtest on historical data."""
    config = ctx.obj["config"]
    logger = ctx.obj["logger"]

    try:
        logger.info(f"Running backtest for {symbol} from {start_date} to {end_date}")

        # Initialize components
        fetcher = VNStockFetcher()
        processor = DataProcessor()

        # Initialize strategy
        if strategy == "volume_breakout":
            strategy_params = config.get("strategy.volume_breakout", {})
            trading_strategy = VolumeBreakoutStrategy(strategy_params)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")

        # Initialize backtest engine
        backtest_config = config.get_backtest_config()
        backtest_engine = SimpleBacktest(
            initial_capital=initial_capital,
            commission=commission,
            slippage=backtest_config.get("slippage", 0.001),
        )

        # Fetch and process data
        click.echo("Fetching data...")
        data = fetcher.fetch(symbol, start_date, end_date)

        if data.empty:
            raise click.ClickException(f"No data available for {symbol}")

        click.echo("Processing data...")
        processed_data = processor.process(data)

        # Run backtest
        click.echo("Running backtest...")
        results = backtest_engine.run(trading_strategy, processed_data)

        if "error" in results:
            raise click.ClickException(f"Backtest failed: {results['error']}")

        # Display results
        _display_backtest_results(results, output)

        # Generate plots if requested
        if plot:
            _generate_backtest_plots(results, symbol)

        logger.info("Backtest completed successfully")

    except Exception as e:
        logger.error(f"Backtest failed: {str(e)}")
        raise click.ClickException(str(e))


@cli.command()
@click.option("--symbol", "-s", help="Specific symbol to update")
@click.option("--days", default=30, help="Number of days to update")
@click.pass_context
def update_data(ctx, symbol, days):
    """Update market data cache."""
    config = ctx.obj["config"]
    logger = ctx.obj["logger"]

    try:
        # Calculate date range
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")

        # Get symbols to update
        if symbol:
            symbols = [symbol]
        else:
            symbols = config.get("data.symbols", ["VIC", "VHM", "VCB", "BID", "CTG"])

        logger.info(f"Updating data for {len(symbols)} symbols")

        # Initialize fetcher
        fetcher = VNStockFetcher()

        # Update data
        with click.progressbar(symbols, label="Updating data") as symbol_list:
            for sym in symbol_list:
                try:
                    data = fetcher.fetch(sym, start_date, end_date)
                    if not data.empty:
                        logger.debug(f"Updated {len(data)} records for {sym}")
                except Exception as e:
                    logger.error(f"Failed to update {sym}: {str(e)}")

        click.echo("Data update completed")

    except Exception as e:
        logger.error(f"Data update failed: {str(e)}")
        raise click.ClickException(str(e))


def _display_scan_results(results: List[dict], format_type: str, output_file: Optional[str]):
    """Display scan results in specified format."""
    if format_type == "json":
        output_data = json.dumps(results, indent=2, default=str)
    elif format_type == "csv":
        df = pd.DataFrame(results)
        output_data = df.to_csv(index=False)
    else:  # table
        df = pd.DataFrame(results)
        # Select key columns for display
        display_cols = [
            "symbol",
            "total_signals",
            "recent_buy_signals",
            "current_volume_ratio",
            "current_price_change",
        ]
        available_cols = [col for col in display_cols if col in df.columns]
        output_data = df[available_cols].to_string(index=False)

    if output_file:
        with open(output_file, "w") as f:
            f.write(output_data)
        click.echo(f"Results saved to {output_file}")
    else:
        click.echo(output_data)


def _display_backtest_results(results: dict, output_file: Optional[str]):
    """Display backtest results."""
    # Format key metrics
    metrics = results.get("metrics", {})

    output_lines = [
        f"Backtest Results for {results.get('strategy', 'Unknown')}",
        "=" * 50,
        f"Period: {results.get('start_date')} to {results.get('end_date')}",
        f"Initial Capital: {results.get('initial_capital'):,.2f}",
        f"Final Capital: {results.get('final_capital'):,.2f}",
        f"Total Return: {results.get('total_return', 0) * 100:.2f}%",
        f"Total Trades: {results.get('total_trades', 0)}",
        "",
        "Performance Metrics:",
        f"  Win Rate: {metrics.get('win_rate_pct', 0):.2f}%",
        f"  Profit Factor: {metrics.get('profit_factor', 0):.2f}",
        f"  Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}",
        f"  Max Drawdown: {metrics.get('max_drawdown_pct', 0):.2f}%",
        f"  Average Win: {metrics.get('avg_win', 0):.2f}",
        f"  Average Loss: {metrics.get('avg_loss', 0):.2f}",
    ]

    output_data = "\n".join(output_lines)

    if output_file:
        with open(output_file, "w") as f:
            f.write(output_data)
        click.echo(f"Results saved to {output_file}")
    else:
        click.echo(output_data)


def _generate_backtest_plots(results: dict, symbol: str):
    """Generate backtest performance plots."""
    try:
        import matplotlib.pyplot as plt

        # Extract equity curve
        equity_curve = results.get("equity_curve", [])
        if not equity_curve:
            click.echo("No equity curve data available for plotting")
            return

        df = pd.DataFrame(equity_curve)
        df["date"] = pd.to_datetime(df["date"])

        # Create plots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        # Equity curve
        ax1.plot(df["date"], df["equity"])
        ax1.set_title(f"Equity Curve - {symbol}")
        ax1.set_ylabel("Equity")
        ax1.grid(True)

        # Price chart
        ax2.plot(df["date"], df["price"])
        ax2.set_title(f"Price Chart - {symbol}")
        ax2.set_ylabel("Price")
        ax2.set_xlabel("Date")
        ax2.grid(True)

        plt.tight_layout()

        # Save plot
        plot_file = f"backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(plot_file)
        click.echo(f"Plot saved to {plot_file}")

    except ImportError:
        click.echo("Matplotlib not available for plotting")
    except Exception as e:
        click.echo(f"Error generating plots: {str(e)}")


if __name__ == "__main__":
    cli()
