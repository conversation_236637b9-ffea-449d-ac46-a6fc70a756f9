["tests/test_cli/test_commands.py::TestCLICommands::test_backtest_command_basic", "tests/test_cli/test_commands.py::TestCLICommands::test_backtest_command_help", "tests/test_cli/test_commands.py::TestCLICommands::test_cli_group_basic", "tests/test_cli/test_commands.py::TestCLICommands::test_cli_group_verbose", "tests/test_cli/test_commands.py::TestCLICommands::test_cli_group_with_config", "tests/test_cli/test_commands.py::TestCLICommands::test_cli_group_with_config_dir", "tests/test_cli/test_commands.py::TestCLICommands::test_scan_command_basic", "tests/test_cli/test_commands.py::TestCLICommands::test_scan_command_help", "tests/test_cli/test_commands.py::TestCLICommands::test_update_data_command_basic", "tests/test_cli/test_commands.py::TestCLICommands::test_update_data_command_help", "tests/test_cli/test_commands.py::TestCLICommandsWithMocking::test_backtest_command_with_mocks", "tests/test_cli/test_commands.py::TestCLICommandsWithMocking::test_scan_command_with_mocks", "tests/test_cli/test_commands.py::TestCLICommandsWithMocking::test_update_data_command_with_mocks", "tests/test_cli/test_commands.py::TestCLIErrorHandling::test_backtest_with_missing_symbol", "tests/test_cli/test_commands.py::TestCLIErrorHandling::test_cli_with_invalid_config", "tests/test_cli/test_commands.py::TestCLIErrorHandling::test_cli_with_invalid_config_dir", "tests/test_cli/test_commands.py::TestCLIErrorHandling::test_scan_with_invalid_date_format", "tests/test_cli/test_commands.py::TestCLIErrorHandling::test_scan_with_invalid_symbol", "tests/test_cli/test_commands.py::TestCLIIntegration::test_cli_command_chain", "tests/test_data/test_storage.py::TestCacheErrorHandling::test_delete_nonexistent_key", "tests/test_data/test_storage.py::TestCacheErrorHandling::test_duckdb_cache_invalid_path", "tests/test_data/test_storage.py::TestCacheErrorHandling::test_memory_cache_error_handling", "tests/test_data/test_storage.py::TestCacheErrorHandling::test_memory_cache_with_none_values", "tests/test_data/test_storage.py::TestCacheIntegration::test_cache_performance_simulation", "tests/test_data/test_storage.py::TestCacheIntegration::test_cache_with_dataframes", "tests/test_data/test_storage.py::TestDuckDBCache::test_clear", "tests/test_data/test_storage.py::TestDuckDBCache::test_complex_data_serialization", "tests/test_data/test_storage.py::TestDuckDBCache::test_delete", "tests/test_data/test_storage.py::TestDuckDBCache::test_duckdb_cache_concurrent_access", "tests/test_data/test_storage.py::TestDuckDBCache::test_duckdb_cache_error_handling", "tests/test_data/test_storage.py::TestDuckDBCache::test_get_nonexistent_key", "tests/test_data/test_storage.py::TestDuckDBCache::test_persistence", "tests/test_data/test_storage.py::TestDuckDBCache::test_set_and_get", "tests/test_data/test_storage.py::TestDuckDBCache::test_ttl_expiration", "tests/test_data/test_storage.py::TestMemoryCache::test_clear", "tests/test_data/test_storage.py::TestMemoryCache::test_complex_data_types", "tests/test_data/test_storage.py::TestMemoryCache::test_delete", "tests/test_data/test_storage.py::TestMemoryCache::test_get_nonexistent_key", "tests/test_data/test_storage.py::TestMemoryCache::test_overwrite_existing_key", "tests/test_data/test_storage.py::TestMemoryCache::test_set_and_get", "tests/test_data/test_storage.py::TestMemoryCache::test_ttl_expiration", "tests/test_data/test_storage.py::TestMemoryCacheAdvanced::test_cache_delete_nonexistent", "tests/test_data/test_storage.py::TestMemoryCacheAdvanced::test_cache_overwrite_with_ttl", "tests/test_data/test_storage.py::TestMemoryCacheAdvanced::test_cache_size_tracking", "tests/test_data/test_storage.py::TestMemoryCacheAdvanced::test_cache_with_large_data", "tests/test_data/test_storage.py::TestMemoryCacheAdvanced::test_ttl_cleanup", "tests/test_functional.py::TestConfiguration::test_config_helper_methods", "tests/test_functional.py::TestConfiguration::test_config_loading_different_environments", "tests/test_functional.py::TestConfiguration::test_config_nested_access", "tests/test_functional.py::TestDataFetcher::test_memory_cache_basic_operations", "tests/test_functional.py::TestDataFetcher::test_memory_cache_ttl", "tests/test_functional.py::TestDataFetcher::test_vnstock_fetcher_initialization", "tests/test_functional.py::TestDataFetcher::test_vnstock_fetcher_mock_response", "tests/test_functional.py::TestDataProcessor::test_data_processor_basic_indicators", "tests/test_functional.py::TestDataProcessor::test_data_processor_volume_breakout_signals", "tests/test_functional.py::TestDataProcessor::test_data_processor_volume_indicators", "tests/test_functional.py::TestLogging::test_logger_initialization", "tests/test_functional.py::TestLogging::test_logger_methods", "tests/test_functional.py::TestSimpleBacktest::test_backtest_initialization", "tests/test_functional.py::TestSimpleBacktest::test_backtest_reset_functionality", "tests/test_functional.py::TestSimpleBacktest::test_backtest_run_with_strategy", "tests/test_functional.py::TestVolumeBreakoutStrategy::test_strategy_analysis", "tests/test_functional.py::TestVolumeBreakoutStrategy::test_strategy_data_validation", "tests/test_functional.py::TestVolumeBreakoutStrategy::test_strategy_initialization", "tests/test_functional.py::TestVolumeBreakoutStrategy::test_strategy_signal_generation", "tests/test_integration.py::TestIntegration::test_backtest_engine", "tests/test_integration.py::TestIntegration::test_config_loading", "tests/test_integration.py::TestIntegration::test_data_processor", "tests/test_integration.py::TestIntegration::test_end_to_end_workflow", "tests/test_integration.py::TestIntegration::test_logger_initialization", "tests/test_integration.py::TestIntegration::test_volume_breakout_strategy", "tests/test_integration.py::test_module_imports", "tests/test_integration_advanced.py::TestEndToEndWorkflow::test_backtest_integration_with_different_strategies", "tests/test_integration_advanced.py::TestEndToEndWorkflow::test_complete_workflow_with_mocked_data", "tests/test_integration_advanced.py::TestEndToEndWorkflow::test_data_processor_integration", "tests/test_integration_advanced.py::TestEndToEndWorkflow::test_strategy_integration_with_different_parameters", "tests/test_integration_advanced.py::TestSystemIntegration::test_caching_integration", "tests/test_integration_advanced.py::TestSystemIntegration::test_config_driven_workflow", "tests/test_integration_advanced.py::TestSystemIntegration::test_error_handling_integration", "tests/test_integration_advanced.py::TestSystemIntegration::test_performance_integration", "tests/test_utils/test_config.py::TestConfig::test_config_helper_methods", "tests/test_utils/test_config.py::TestConfig::test_config_merge_deep", "tests/test_utils/test_config.py::TestConfig::test_config_reload", "tests/test_utils/test_config.py::TestConfig::test_config_save", "tests/test_utils/test_config.py::TestConfig::test_config_update", "tests/test_utils/test_config.py::TestConfig::test_config_validation", "tests/test_utils/test_config.py::TestConfig::test_config_with_lists_and_complex_types", "tests/test_utils/test_config.py::TestConfig::test_environment_variable_override", "tests/test_utils/test_config.py::TestConfig::test_get_all", "tests/test_utils/test_config.py::TestConfig::test_get_nested_keys", "tests/test_utils/test_config.py::TestConfig::test_get_section", "tests/test_utils/test_config.py::TestConfig::test_get_with_default_value", "tests/test_utils/test_config.py::TestConfig::test_has_key", "tests/test_utils/test_config.py::TestConfig::test_invalid_yaml_file", "tests/test_utils/test_config.py::TestConfig::test_load_default_config", "tests/test_utils/test_config.py::TestConfig::test_load_development_config", "tests/test_utils/test_config.py::TestConfig::test_load_production_config", "tests/test_utils/test_config.py::TestConfig::test_missing_config_file", "tests/test_utils/test_config.py::TestConfig::test_set_value", "tests/test_utils/test_config.py::TestConfig::test_to_dict", "tests/test_utils/test_config.py::TestConfigErrorHandling::test_config_get_with_invalid_key_format", "tests/test_utils/test_config.py::TestConfigErrorHandling::test_config_with_empty_directory", "tests/test_utils/test_config.py::TestConfigErrorHandling::test_config_with_permission_error", "tests/test_utils/test_config.py::TestConfigIntegration::test_config_with_real_world_structure", "tests/test_utils/test_helpers.py::TestCacheHelpers::test_generate_cache_key_basic", "tests/test_utils/test_helpers.py::TestCacheHelpers::test_generate_cache_key_with_complex_data", "tests/test_utils/test_helpers.py::TestCacheHelpers::test_generate_cache_key_with_kwargs", "tests/test_utils/test_helpers.py::TestCalculationHelpers::test_calculate_returns", "tests/test_utils/test_helpers.py::TestCalculationHelpers::test_calculate_returns_log", "tests/test_utils/test_helpers.py::TestCalculationHelpers::test_calculate_volatility", "tests/test_utils/test_helpers.py::TestCalculationHelpers::test_safe_divide", "tests/test_utils/test_helpers.py::TestDataHelpers::test_chunk_list", "tests/test_utils/test_helpers.py::TestDataHelpers::test_flatten_dict", "tests/test_utils/test_helpers.py::TestDataHelpers::test_merge_dicts", "tests/test_utils/test_helpers.py::TestDataHelpers::test_unflatten_dict", "tests/test_utils/test_helpers.py::TestDateHelpers::test_get_business_days", "tests/test_utils/test_helpers.py::TestDateHelpers::test_validate_date_format_custom_format", "tests/test_utils/test_helpers.py::TestDateHelpers::test_validate_date_format_invalid", "tests/test_utils/test_helpers.py::TestDateHelpers::test_validate_date_format_valid", "tests/test_utils/test_helpers.py::TestFormatHelpers::test_format_currency", "tests/test_utils/test_helpers.py::TestFormatHelpers::test_format_percentage", "tests/test_utils/test_helpers.py::TestFormatHelpers::test_format_percentage_custom_decimals", "tests/test_utils/test_helpers.py::TestSymbolHelpers::test_normalize_symbol", "tests/test_utils/test_helpers.py::TestUtilityHelpers::test_get_memory_usage", "tests/test_utils/test_helpers.py::TestUtilityHelpers::test_timing_decorator"]