#!/usr/bin/env python3
"""
Scanner script for Augment Raptor

This script runs the market scanner to identify trading opportunities
using the Volume Breakout strategy.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import argparse

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from augment_raptor.utils.config import Config
from augment_raptor.utils.logger import Logger
from augment_raptor.data.fetcher import VNStockFetcher
from augment_raptor.data.processor import DataProcessor
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy


def main():
    """Main scanner function."""
    parser = argparse.ArgumentParser(description="Augment Raptor Market Scanner")
    parser.add_argument("--config-dir", default="config", help="Configuration directory")
    parser.add_argument("--environment", default="development", help="Environment")
    parser.add_argument("--symbols", nargs="+", help="Symbols to scan")
    parser.add_argument("--days", type=int, default=90, help="Days of data to analyze")
    parser.add_argument("--output", help="Output file for results")
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = Config(config_dir=args.config_dir, environment=args.environment)
        
        # Configure logging
        Logger.configure_from_config(config.get_logging_config())
        logger = Logger(__name__)
        
        logger.info("Starting market scanner")
        
        # Calculate date range
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
        
        # Get symbols to scan
        symbols = args.symbols or config.get('scanner.default_symbols', ['VIC', 'VHM', 'VCB'])
        
        logger.info(f"Scanning {len(symbols)} symbols from {start_date} to {end_date}")
        
        # Initialize components
        fetcher = VNStockFetcher()
        processor = DataProcessor()
        
        # Initialize strategy
        strategy_params = config.get('strategy.volume_breakout', {})
        strategy = VolumeBreakoutStrategy(strategy_params)
        
        # Scan symbols
        results = []
        
        for i, symbol in enumerate(symbols, 1):
            try:
                logger.info(f"Scanning {symbol} ({i}/{len(symbols)})")
                
                # Fetch data
                data = fetcher.fetch(symbol, start_date, end_date)
                
                if data.empty:
                    logger.warning(f"No data for {symbol}")
                    continue
                
                # Process data
                processed_data = processor.process(data)
                
                # Analyze with strategy
                analysis = strategy.analyze(processed_data)
                
                if 'error' not in analysis:
                    analysis['symbol'] = symbol
                    results.append(analysis)
                    
                    # Log interesting signals
                    if analysis.get('recent_buy_signals', 0) > 0:
                        logger.info(f"Found {analysis['recent_buy_signals']} recent buy signals for {symbol}")
                
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {str(e)}")
        
        # Display results
        if results:
            print(f"\nScan Results ({len(results)} symbols analyzed):")
            print("=" * 80)
            print(f"{'Symbol':<10} {'Signals':<8} {'Recent':<8} {'Vol Ratio':<12} {'Price Chg':<12}")
            print("-" * 80)
            
            # Sort by recent signals and volume ratio
            results.sort(key=lambda x: (x.get('recent_buy_signals', 0), x.get('current_volume_ratio', 0)), reverse=True)
            
            for result in results:
                symbol = result['symbol']
                total_signals = result.get('total_signals', 0)
                recent_signals = result.get('recent_buy_signals', 0)
                vol_ratio = result.get('current_volume_ratio', 0)
                price_change = result.get('current_price_change', 0)
                
                print(f"{symbol:<10} {total_signals:<8} {recent_signals:<8} {vol_ratio:<12.2f} {price_change:<12.2f}%")
            
            # Save to file if requested
            if args.output:
                import json
                with open(args.output, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                logger.info(f"Results saved to {args.output}")
            
            logger.info(f"Scanner completed: {len(results)} symbols analyzed")
        else:
            print("No results found")
            
    except Exception as e:
        logger.error(f"Scanner failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
