"""
Configuration management module.

This module handles loading and managing configuration from YAML files
with support for environment-specific overrides and command-line arguments.
"""

import os
import yaml
import argparse
from typing import Dict, Any, Optional
from pathlib import Path


class Config:
    """Configuration manager for the application."""

    def __init__(self, config_dir: str = "config", environment: str = "development"):
        """Initialize configuration manager.

        Args:
            config_dir: Directory containing configuration files
            environment: Environment name (development, production, etc.)
        """
        self.config_dir = Path(config_dir)
        self.environment = environment
        self._config = {}

        # Load configuration
        self._load_config()

    def _load_config(self) -> None:
        """Load configuration from files."""
        # Load default configuration
        default_config_path = self.config_dir / "default.yaml"
        if default_config_path.exists():
            with open(default_config_path, "r", encoding="utf-8") as f:
                self._config = yaml.safe_load(f) or {}

        # Load environment-specific configuration
        env_config_path = self.config_dir / f"{self.environment}.yaml"
        if env_config_path.exists():
            with open(env_config_path, "r", encoding="utf-8") as f:
                env_config = yaml.safe_load(f) or {}
                self._config = self._deep_merge(self._config, env_config)

        # Override with environment variables
        self._load_env_overrides()

    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()

        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value

        return result

    def _load_env_overrides(self) -> None:
        """Load configuration overrides from environment variables."""
        # Look for environment variables with AUGMENT_RAPTOR_ prefix
        prefix = "AUGMENT_RAPTOR_"

        for key, value in os.environ.items():
            if key.startswith(prefix):
                # Convert AUGMENT_RAPTOR_DATA_API_URL to data.api.url
                config_key = key[len(prefix) :].lower().replace("_", ".")
                self._set_nested_value(config_key, value)

    def _set_nested_value(self, key_path: str, value: str) -> None:
        """Set a nested configuration value using dot notation."""
        keys = key_path.split(".")
        current = self._config

        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        # Set the final value (try to convert to appropriate type)
        final_key = keys[-1]
        current[final_key] = self._convert_value(value)

    def _convert_value(self, value: str) -> Any:
        """Convert string value to appropriate type."""
        # Try boolean
        if value.lower() in ("true", "false"):
            return value.lower() == "true"

        # Try integer
        try:
            return int(value)
        except ValueError:
            pass

        # Try float
        try:
            return float(value)
        except ValueError:
            pass

        # Return as string
        return value

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation.

        Args:
            key: Configuration key (e.g., 'data.api.url')
            default: Default value if key not found

        Returns:
            Configuration value or default
        """
        keys = key.split(".")
        current = self._config

        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation.

        Args:
            key: Configuration key (e.g., 'data.api.url')
            value: Value to set
        """
        self._set_nested_value(key, value)

    def get_all(self) -> Dict[str, Any]:
        """Get all configuration as dictionary."""
        return self._config.copy()

    def update(self, config_dict: Dict[str, Any]) -> None:
        """Update configuration with dictionary.

        Args:
            config_dict: Dictionary to merge with current configuration
        """
        self._config = self._deep_merge(self._config, config_dict)

    def save(self, file_path: str) -> None:
        """Save current configuration to file.

        Args:
            file_path: Path to save configuration file
        """
        with open(file_path, "w", encoding="utf-8") as f:
            yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)

    @classmethod
    def from_args(cls, args: Optional[argparse.Namespace] = None) -> "Config":
        """Create configuration from command line arguments.

        Args:
            args: Parsed command line arguments

        Returns:
            Config instance
        """
        if args is None:
            parser = argparse.ArgumentParser()
            parser.add_argument("--config-dir", default="config", help="Configuration directory")
            parser.add_argument("--environment", default="development", help="Environment name")
            args = parser.parse_args()

        config = cls(
            config_dir=getattr(args, "config_dir", "config"),
            environment=getattr(args, "environment", "development"),
        )

        # Override with any additional command line arguments
        for key, value in vars(args).items():
            if key not in ("config_dir", "environment") and value is not None:
                # Convert argument name to config key
                config_key = key.replace("_", ".")
                config.set(config_key, value)

        return config

    def validate_required_keys(self, required_keys: list) -> None:
        """Validate that required configuration keys are present.

        Args:
            required_keys: List of required configuration keys

        Raises:
            ValueError: If any required key is missing
        """
        missing_keys = []

        for key in required_keys:
            if self.get(key) is None:
                missing_keys.append(key)

        if missing_keys:
            raise ValueError(f"Missing required configuration keys: {missing_keys}")

    def get_data_config(self) -> Dict[str, Any]:
        """Get data-related configuration."""
        return self.get("data", {})

    def get_strategy_config(self) -> Dict[str, Any]:
        """Get strategy-related configuration."""
        return self.get("strategy", {})

    def get_backtest_config(self) -> Dict[str, Any]:
        """Get backtest-related configuration."""
        return self.get("backtest", {})

    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging-related configuration."""
        return self.get("logging", {})
