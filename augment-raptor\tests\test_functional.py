"""
Functional tests for individual components of Augment Raptor

This module contains functional tests for each component to ensure
they work correctly in isolation.
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from augment_raptor.data.fetcher import <PERSON>Fetcher, VNStockFetcher
from augment_raptor.data.processor import DataProcessor
from augment_raptor.data.storage import MemoryCache
from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
from augment_raptor.backtest.simple_backtest import SimpleBacktest
from augment_raptor.utils.config import Config
from augment_raptor.utils.logger import Logger


class TestDataFetcher:
    """Functional tests for data fetcher components."""

    def test_memory_cache_basic_operations(self):
        """Test basic cache operations."""
        cache = MemoryCache()

        # Test set and get
        cache.set("test_key", "test_value")
        assert cache.get("test_key") == "test_value"

        # Test non-existent key
        assert cache.get("non_existent") is None

        # Test delete
        cache.delete("test_key")
        assert cache.get("test_key") is None

        # Test clear
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.clear()
        assert cache.get("key1") is None
        assert cache.get("key2") is None

    def test_memory_cache_ttl(self):
        """Test cache TTL functionality."""
        import time

        cache = MemoryCache()

        # Set with short TTL
        cache.set("ttl_key", "ttl_value", ttl=1)  # 1 second
        assert cache.get("ttl_key") == "ttl_value"

        # Wait for expiry (simulate)
        # Note: In real test, we'd wait, but for speed we'll test the logic
        # by setting a past expiry time manually
        cache._cache["ttl_key"] = ("ttl_value", time.time() - 1)
        assert cache.get("ttl_key") is None

    def test_vnstock_fetcher_initialization(self):
        """Test VNStock fetcher initialization."""
        fetcher = VNStockFetcher()

        assert fetcher.base_url == "https://api.vnstock.vn"
        assert fetcher.timeout == 30
        assert hasattr(fetcher, "session")
        assert hasattr(fetcher, "logger")

    @patch("requests.Session.get")
    def test_vnstock_fetcher_mock_response(self, mock_get):
        """Test VNStock fetcher with mocked response."""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = [
            {
                "date": "2024-01-01",
                "open": 100,
                "high": 105,
                "low": 98,
                "close": 102,
                "volume": 1000000,
            },
            {
                "date": "2024-01-02",
                "open": 102,
                "high": 106,
                "low": 100,
                "close": 104,
                "volume": 1200000,
            },
        ]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        fetcher = VNStockFetcher()
        result = fetcher.fetch("VIC", "2024-01-01", "2024-01-02")

        # Verify result structure
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 2
        expected_columns = ["Open", "High", "Low", "Close", "Volume"]
        for col in expected_columns:
            assert col in result.columns


class TestDataProcessor:
    """Functional tests for data processor."""

    def create_sample_data(self, n_rows=60):
        """Create sample OHLCV data for testing."""
        np.random.seed(42)

        # Generate realistic price data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        return pd.DataFrame(
            {
                "Open": prices[:-1],
                "High": [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
                "Low": [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
                "Close": prices[1:],
                "Volume": [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)],
            }
        )

    def test_data_processor_basic_indicators(self):
        """Test basic indicator calculation."""
        processor = DataProcessor()
        sample_data = self.create_sample_data()

        processed_data = processor.process(sample_data)

        # Check basic indicators
        expected_indicators = [
            "Price_Change",
            "Price_Change_Pct",
            "True_Range",
            "ATR_14",
            "SMA_5",
            "SMA_10",
            "SMA_20",
            "SMA_50",
        ]

        for indicator in expected_indicators:
            assert indicator in processed_data.columns, f"Missing indicator: {indicator}"

    def test_data_processor_volume_indicators(self):
        """Test volume indicator calculation."""
        processor = DataProcessor()
        sample_data = self.create_sample_data()

        processed_data = processor.process(sample_data)

        # Check volume indicators
        volume_indicators = [
            "Volume_SMA_5",
            "Volume_SMA_10",
            "Volume_SMA_20",
            "Volume_SMA_50",
            "Volume_Ratio_20",
            "VPT",
            "OBV",
            "MFI_14",
        ]

        for indicator in volume_indicators:
            assert indicator in processed_data.columns, f"Missing volume indicator: {indicator}"

    def test_data_processor_volume_breakout_signals(self):
        """Test volume breakout signal calculation."""
        processor = DataProcessor()
        sample_data = self.create_sample_data()

        # First process the data to get required indicators
        processed_data = processor.process(sample_data)
        # Then calculate volume breakout signals
        processed_data = processor.calculate_volume_breakout_signals(processed_data)

        # Check signal columns
        assert "Volume_Breakout_Signal" in processed_data.columns
        assert "Signal_Strength" in processed_data.columns

        # Verify signal values are boolean/numeric
        assert processed_data["Volume_Breakout_Signal"].dtype == bool
        assert processed_data["Signal_Strength"].dtype in [np.float64, float]


class TestVolumeBreakoutStrategy:
    """Functional tests for Volume Breakout strategy."""

    def create_strategy_test_data(self, n_rows=60):
        """Create test data suitable for strategy testing."""
        np.random.seed(42)

        # Generate realistic price data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        return pd.DataFrame(
            {
                "Open": prices[:-1],
                "High": [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
                "Low": [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
                "Close": prices[1:],
                "Volume": [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)],
            }
        )

    def test_strategy_initialization(self):
        """Test strategy initialization with different parameters."""
        # Default parameters
        strategy1 = VolumeBreakoutStrategy()
        assert strategy1.name == "Volume Breakout"
        assert strategy1.get_parameters()["volume_threshold"] == 2.0

        # Custom parameters
        custom_params = {"volume_threshold": 3.0, "price_threshold": 0.03, "min_volume": 200000}
        strategy2 = VolumeBreakoutStrategy(custom_params)
        assert strategy2.get_parameters()["volume_threshold"] == 3.0
        assert strategy2.get_parameters()["price_threshold"] == 0.03
        assert strategy2.get_parameters()["min_volume"] == 200000

    def test_strategy_data_validation(self):
        """Test strategy data validation."""
        strategy = VolumeBreakoutStrategy()

        # Valid data
        valid_data = self.create_strategy_test_data(60)
        assert strategy.validate_data(valid_data) == True

        # Invalid data - too few rows
        invalid_data = self.create_strategy_test_data(10)
        assert strategy.validate_data(invalid_data) == False

        # Invalid data - missing columns
        incomplete_data = pd.DataFrame({"Close": [100, 101, 102]})
        assert strategy.validate_data(incomplete_data) == False

        # Empty data
        empty_data = pd.DataFrame()
        assert strategy.validate_data(empty_data) == False

    def test_strategy_signal_generation(self):
        """Test signal generation functionality."""
        strategy = VolumeBreakoutStrategy()
        test_data = self.create_strategy_test_data()

        signals = strategy.generate_signals(test_data)

        # Check signal columns exist
        required_columns = ["signal", "signal_strength", "entry_price", "stop_loss", "take_profit"]
        for col in required_columns:
            assert col in signals.columns, f"Missing signal column: {col}"

        # Check signal values are valid
        assert signals["signal"].isin([-1, 0, 1]).all(), "Invalid signal values"
        assert (signals["signal_strength"] >= 0).all(), "Signal strength should be non-negative"

    def test_strategy_analysis(self):
        """Test strategy analysis functionality."""
        strategy = VolumeBreakoutStrategy()
        test_data = self.create_strategy_test_data()

        analysis = strategy.analyze(test_data)

        # Check analysis results
        expected_keys = [
            "strategy",
            "total_signals",
            "buy_signals",
            "sell_signals",
            "signal_strength_avg",
            "parameters",
            "data_points",
        ]

        for key in expected_keys:
            assert key in analysis, f"Missing analysis key: {key}"

        assert analysis["strategy"] == "Volume Breakout"
        assert isinstance(analysis["total_signals"], (int, np.integer))
        assert isinstance(analysis["data_points"], (int, np.integer))


class TestSimpleBacktest:
    """Functional tests for backtest engine."""

    def create_backtest_data(self, n_rows=60):
        """Create test data for backtesting."""
        np.random.seed(42)
        dates = pd.date_range("2024-01-01", periods=n_rows, freq="D")

        # Generate realistic price data
        prices = [100]
        for i in range(n_rows - 1):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 10))

        return pd.DataFrame(
            {
                "Open": prices[:-1],
                "High": [p * np.random.uniform(1.0, 1.05) for p in prices[:-1]],
                "Low": [p * np.random.uniform(0.95, 1.0) for p in prices[:-1]],
                "Close": prices[1:],
                "Volume": [np.random.randint(500000, 5000000) for _ in range(n_rows - 1)],
            },
            index=dates[:-1],
        )

    def test_backtest_initialization(self):
        """Test backtest engine initialization."""
        backtest = SimpleBacktest(initial_capital=100000, commission=0.001, slippage=0.001)

        assert backtest.initial_capital == 100000
        assert backtest.commission == 0.001
        assert backtest.slippage == 0.001
        assert backtest.capital == 100000
        assert backtest.position == 0.0

    def test_backtest_run_with_strategy(self):
        """Test running backtest with strategy."""
        backtest = SimpleBacktest(initial_capital=100000)
        strategy = VolumeBreakoutStrategy()
        test_data = self.create_backtest_data()

        results = backtest.run(strategy, test_data)

        # Check results structure
        expected_keys = [
            "strategy",
            "start_date",
            "end_date",
            "initial_capital",
            "final_capital",
            "total_return",
            "total_trades",
            "metrics",
        ]

        for key in expected_keys:
            assert key in results, f"Missing result key: {key}"

        assert results["initial_capital"] == 100000
        assert isinstance(results["total_return"], float)
        assert isinstance(results["total_trades"], int)

    def test_backtest_reset_functionality(self):
        """Test backtest reset functionality."""
        backtest = SimpleBacktest(initial_capital=50000)

        # Modify state
        backtest.capital = 60000
        backtest.position = 100
        backtest.trades = [{"test": "trade"}]

        # Reset
        backtest.reset()

        # Verify reset
        assert backtest.capital == 50000
        assert backtest.position == 0.0
        assert backtest.trades == []
        assert backtest.equity_curve == []


class TestConfiguration:
    """Functional tests for configuration system."""

    def test_config_loading_different_environments(self):
        """Test loading configuration for different environments."""
        # Development config
        dev_config = Config(config_dir="config", environment="development")
        assert dev_config.get("app.environment") == "development"

        # Production config
        prod_config = Config(config_dir="config", environment="production")
        assert prod_config.get("app.environment") == "production"

    def test_config_nested_access(self):
        """Test nested configuration access."""
        config = Config(config_dir="config", environment="development")

        # Test nested access
        assert config.get("strategy.volume_breakout.volume_threshold") == 1.5  # dev value
        assert config.get("data.cache.ttl") == 3600  # dev value

        # Test non-existent key
        assert config.get("non.existent.key") is None
        assert config.get("non.existent.key", "default") == "default"

    def test_config_helper_methods(self):
        """Test configuration helper methods."""
        config = Config(config_dir="config", environment="development")

        # Test helper methods
        strategy_config = config.get_strategy_config()
        assert "volume_breakout" in strategy_config

        data_config = config.get_data_config()
        assert "api" in data_config

        backtest_config = config.get_backtest_config()
        assert "initial_capital" in backtest_config


class TestLogging:
    """Functional tests for logging system."""

    def test_logger_initialization(self):
        """Test logger initialization."""
        logger = Logger("test_module")

        assert logger.name == "test_module"
        assert hasattr(logger, "logger")
        assert hasattr(logger, "info")
        assert hasattr(logger, "error")
        assert hasattr(logger, "debug")
        assert hasattr(logger, "warning")

    def test_logger_methods(self):
        """Test logger methods don't crash."""
        logger = Logger("test_module")

        # Test all log levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")

        # Test structured logging
        logger.info("Structured message", param1="value1", param2=123)

        # Test performance logging
        logger.log_performance("test_operation", 1.5, extra_param="value")


if __name__ == "__main__":
    # Run all functional tests
    pytest.main([__file__, "-v"])
