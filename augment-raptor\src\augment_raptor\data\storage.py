"""
Storage and caching module for market data.

This module provides different caching strategies including in-memory
and persistent storage using DuckDB.
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, Dict, Tuple
import time
import pickle
import os
from pathlib import Path
from ..utils.logger import Logger

# Try to import duckdb, fallback to None if not available
try:
    import duckdb
    DUCKDB_AVAILABLE = True
except ImportError:
    duckdb = None
    DUCKDB_AVAILABLE = False


class Cache(ABC):
    """Abstract base class for cache implementations."""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> None:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear all values from cache."""
        pass


class MemoryCache(Cache):
    """In-memory cache implementation."""
    
    def __init__(self):
        self._cache: Dict[str, Tuple[Any, Optional[float]]] = {}
        self.logger = Logger(__name__)
        
    def get(self, key: str) -> Optional[Any]:
        if key not in self._cache:
            return None
            
        value, expiry = self._cache[key]
        
        # Check if expired
        if expiry is not None and time.time() > expiry:
            self.delete(key)
            return None
            
        return value
        
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        expiry = time.time() + ttl if ttl is not None else None
        self._cache[key] = (value, expiry)
        self.logger.debug(f"Cached key: {key}")
        
    def delete(self, key: str) -> None:
        if key in self._cache:
            del self._cache[key]
            self.logger.debug(f"Deleted key: {key}")
            
    def clear(self) -> None:
        self._cache.clear()
        self.logger.info("Memory cache cleared")


class DuckDBCache(Cache):
    """DuckDB-based persistent cache implementation."""

    def __init__(self, db_path: str = "data/cache/cache.db"):
        if not DUCKDB_AVAILABLE:
            raise ImportError(
                "DuckDB is not available. Please install duckdb package or use MemoryCache instead."
            )

        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        self.conn = duckdb.connect(str(self.db_path))
        self.logger = Logger(__name__)
        self._create_tables()
        
    def _create_tables(self) -> None:
        """Create cache tables if they don't exist."""
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS cache (
                key VARCHAR PRIMARY KEY,
                value BLOB,
                expiry DOUBLE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
    def get(self, key: str) -> Optional[Any]:
        try:
            result = self.conn.execute(
                "SELECT value, expiry FROM cache WHERE key = ?", 
                [key]
            ).fetchone()
            
            if result is None:
                return None
                
            value_blob, expiry = result
            
            # Check if expired
            if expiry is not None and time.time() > expiry:
                self.delete(key)
                return None
                
            return pickle.loads(value_blob)
            
        except Exception as e:
            self.logger.error(f"Error getting cache key {key}: {str(e)}")
            return None
        
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        try:
            expiry = time.time() + ttl if ttl is not None else None
            value_blob = pickle.dumps(value)
            
            self.conn.execute(
                """
                INSERT OR REPLACE INTO cache (key, value, expiry)
                VALUES (?, ?, ?)
                """,
                [key, value_blob, expiry]
            )
            
            self.logger.debug(f"Cached key: {key}")
            
        except Exception as e:
            self.logger.error(f"Error setting cache key {key}: {str(e)}")
            
    def delete(self, key: str) -> None:
        try:
            self.conn.execute("DELETE FROM cache WHERE key = ?", [key])
            self.logger.debug(f"Deleted key: {key}")
            
        except Exception as e:
            self.logger.error(f"Error deleting cache key {key}: {str(e)}")
            
    def clear(self) -> None:
        try:
            self.conn.execute("DELETE FROM cache")
            self.logger.info("DuckDB cache cleared")
            
        except Exception as e:
            self.logger.error(f"Error clearing cache: {str(e)}")
    
    def cleanup_expired(self) -> None:
        """Remove expired cache entries."""
        try:
            current_time = time.time()
            result = self.conn.execute(
                "DELETE FROM cache WHERE expiry IS NOT NULL AND expiry < ?",
                [current_time]
            )
            
            self.logger.info(f"Cleaned up expired cache entries")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up expired cache: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            stats = {}
            
            # Total entries
            result = self.conn.execute("SELECT COUNT(*) FROM cache").fetchone()
            stats['total_entries'] = result[0] if result else 0
            
            # Expired entries
            current_time = time.time()
            result = self.conn.execute(
                "SELECT COUNT(*) FROM cache WHERE expiry IS NOT NULL AND expiry < ?",
                [current_time]
            ).fetchone()
            stats['expired_entries'] = result[0] if result else 0
            
            # Database size
            if self.db_path.exists():
                stats['db_size_mb'] = self.db_path.stat().st_size / (1024 * 1024)
            else:
                stats['db_size_mb'] = 0
                
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting cache stats: {str(e)}")
            return {}
    
    def close(self) -> None:
        """Close database connection."""
        if self.conn:
            self.conn.close()
            self.logger.info("DuckDB cache connection closed")
