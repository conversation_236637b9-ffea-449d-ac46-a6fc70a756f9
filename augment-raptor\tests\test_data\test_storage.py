"""
Tests for data.storage module.
"""

import pytest
import tempfile
import os
import time
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

from augment_raptor.data.storage import (
    <PERSON>ache,
    MemoryCache,
    Duck<PERSON>BCache,
    DUCKDB_AVAILABLE
)


class TestMemoryCache:
    """Test MemoryCache implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.cache = MemoryCache()
        
    def test_set_and_get(self):
        """Test basic set and get operations."""
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"
        
    def test_get_nonexistent_key(self):
        """Test getting non-existent key."""
        assert self.cache.get("nonexistent") is None
        
    def test_delete(self):
        """Test delete operation."""
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"
        
        self.cache.delete("key1")
        assert self.cache.get("key1") is None
        
    def test_clear(self):
        """Test clear operation."""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        self.cache.clear()
        
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") is None
        
    def test_ttl_expiration(self):
        """Test TTL expiration."""
        # Set with very short TTL
        self.cache.set("key1", "value1", ttl=1)
        
        # Should be available immediately
        assert self.cache.get("key1") == "value1"
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Should be expired
        assert self.cache.get("key1") is None
        
    def test_complex_data_types(self):
        """Test storing complex data types."""
        data = {
            "list": [1, 2, 3],
            "dict": {"nested": "value"},
            "dataframe": pd.DataFrame({"col": [1, 2, 3]})
        }
        
        self.cache.set("complex", data)
        retrieved = self.cache.get("complex")
        
        assert retrieved["list"] == [1, 2, 3]
        assert retrieved["dict"]["nested"] == "value"
        assert retrieved["dataframe"].equals(data["dataframe"])
        
    def test_overwrite_existing_key(self):
        """Test overwriting existing key."""
        self.cache.set("key1", "value1")
        self.cache.set("key1", "value2")
        
        assert self.cache.get("key1") == "value2"


@pytest.mark.skipif(not DUCKDB_AVAILABLE, reason="DuckDB not available")
class TestDuckDBCache:
    """Test DuckDBCache implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Use temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_file.close()
        self.cache = DuckDBCache(db_path=self.temp_file.name)
        
    def teardown_method(self):
        """Clean up test fixtures."""
        self.cache.close()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
            
    def test_set_and_get(self):
        """Test basic set and get operations."""
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"
        
    def test_get_nonexistent_key(self):
        """Test getting non-existent key."""
        assert self.cache.get("nonexistent") is None
        
    def test_delete(self):
        """Test delete operation."""
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"
        
        self.cache.delete("key1")
        assert self.cache.get("key1") is None
        
    def test_clear(self):
        """Test clear operation."""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        self.cache.clear()
        
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") is None
        
    def test_ttl_expiration(self):
        """Test TTL expiration."""
        # Set with very short TTL
        self.cache.set("key1", "value1", ttl=1)
        
        # Should be available immediately
        assert self.cache.get("key1") == "value1"
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Should be expired
        assert self.cache.get("key1") is None
        
    def test_persistence(self):
        """Test data persistence across cache instances."""
        # Set data in first instance
        self.cache.set("persistent_key", "persistent_value")
        self.cache.close()
        
        # Create new instance with same DB file
        new_cache = DuckDBCache(db_path=self.temp_file.name)
        
        # Data should still be there
        assert new_cache.get("persistent_key") == "persistent_value"
        
        new_cache.close()

    def test_duckdb_cache_error_handling(self):
        """Test DuckDBCache error handling."""
        # Test with invalid data that might cause serialization issues
        problematic_data = {
            'function': lambda x: x,  # Non-serializable
            'normal': 'data'
        }

        # Should handle serialization errors gracefully
        try:
            self.cache.set("problematic", problematic_data)
            # If it succeeds, that's fine too
        except Exception:
            # Should not crash the application
            pass

        # Normal data should still work
        self.cache.set("normal", "test_data")
        assert self.cache.get("normal") == "test_data"

    def test_duckdb_cache_concurrent_access(self):
        """Test DuckDBCache with concurrent access simulation."""
        # Set multiple items rapidly
        for i in range(50):
            self.cache.set(f"concurrent_{i}", f"value_{i}")

        # Verify all items are stored
        for i in range(50):
            assert self.cache.get(f"concurrent_{i}") == f"value_{i}"

        # Delete some items
        for i in range(0, 50, 2):
            self.cache.delete(f"concurrent_{i}")

        # Verify deletions
        for i in range(50):
            if i % 2 == 0:
                assert self.cache.get(f"concurrent_{i}") is None
            else:
                assert self.cache.get(f"concurrent_{i}") == f"value_{i}"
        
    def test_complex_data_serialization(self):
        """Test serialization of complex data types."""
        data = {
            "list": [1, 2, 3],
            "dict": {"nested": "value"},
            "dataframe": pd.DataFrame({"col": [1, 2, 3]})
        }
        
        self.cache.set("complex", data)
        retrieved = self.cache.get("complex")
        
        assert retrieved["list"] == [1, 2, 3]
        assert retrieved["dict"]["nested"] == "value"
        assert retrieved["dataframe"].equals(data["dataframe"])





class TestCacheErrorHandling:
    """Test cache error handling scenarios."""
    
    def test_memory_cache_with_none_values(self):
        """Test MemoryCache handling None values."""
        cache = MemoryCache()
        
        # Setting None should work
        cache.set("key1", None)
        assert cache.get("key1") is None
        
        # But should be distinguishable from missing key
        # (This is a limitation - both return None)
        
    def test_delete_nonexistent_key(self):
        """Test deleting non-existent key doesn't raise error."""
        cache = MemoryCache()
        
        # Should not raise exception
        cache.delete("nonexistent")
        
    @pytest.mark.skipif(not DUCKDB_AVAILABLE, reason="DuckDB not available")
    def test_duckdb_cache_invalid_path(self):
        """Test DuckDBCache with invalid path."""
        # Try to create cache in non-existent directory
        with pytest.raises(Exception):
            DuckDBCache(db_path="/nonexistent/path/cache.db")
            
    def test_memory_cache_error_handling(self):
        """Test MemoryCache error handling."""
        cache = MemoryCache()

        # Test with various data types
        cache.set("string", "test")
        cache.set("number", 123)
        cache.set("list", [1, 2, 3])
        cache.set("dict", {"key": "value"})

        assert cache.get("string") == "test"
        assert cache.get("number") == 123
        assert cache.get("list") == [1, 2, 3]
        assert cache.get("dict") == {"key": "value"}


class TestMemoryCacheAdvanced:
    """Test advanced MemoryCache functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.cache = MemoryCache()

    def test_cache_size_tracking(self):
        """Test cache size tracking."""
        # Add multiple items
        for i in range(10):
            self.cache.set(f"key_{i}", f"value_{i}")

        # Cache should have items
        assert len(self.cache._cache) == 10

        # Clear and verify
        self.cache.clear()
        assert len(self.cache._cache) == 0

    def test_ttl_cleanup(self):
        """Test TTL cleanup functionality."""
        # Set items with different TTLs
        self.cache.set("short_ttl", "value1", ttl=1)
        self.cache.set("long_ttl", "value2", ttl=10)
        self.cache.set("no_ttl", "value3")

        # Wait for short TTL to expire
        time.sleep(1.1)

        # Check which items remain
        assert self.cache.get("short_ttl") is None
        assert self.cache.get("long_ttl") == "value2"
        assert self.cache.get("no_ttl") == "value3"

    def test_cache_overwrite_with_ttl(self):
        """Test overwriting cached items with different TTLs."""
        # Set initial value
        self.cache.set("key1", "value1", ttl=10)
        assert self.cache.get("key1") == "value1"

        # Overwrite with shorter TTL
        self.cache.set("key1", "value2", ttl=1)
        assert self.cache.get("key1") == "value2"

        # Wait for TTL to expire
        time.sleep(1.1)
        assert self.cache.get("key1") is None

    def test_cache_with_large_data(self):
        """Test cache with large data objects."""
        # Create large data object
        large_data = {f"key_{i}": f"value_{i}" * 100 for i in range(100)}

        self.cache.set("large_object", large_data)
        retrieved = self.cache.get("large_object")

        assert retrieved == large_data
        assert len(retrieved) == 100

    def test_cache_delete_nonexistent(self):
        """Test deleting non-existent keys."""
        # Should not raise exception
        self.cache.delete("nonexistent_key")

        # Cache should remain empty
        assert len(self.cache._cache) == 0


class TestCacheIntegration:
    """Test cache integration scenarios."""
    
    def test_cache_with_dataframes(self):
        """Test caching pandas DataFrames."""
        cache = MemoryCache()
        
        df = pd.DataFrame({
            'symbol': ['VIC', 'VCB', 'HPG'],
            'price': [100, 200, 300],
            'volume': [1000, 2000, 3000]
        })
        
        cache.set("market_data", df)
        retrieved_df = cache.get("market_data")
        
        assert retrieved_df.equals(df)
        assert list(retrieved_df.columns) == ['symbol', 'price', 'volume']
        
    def test_cache_performance_simulation(self):
        """Test cache performance with many operations."""
        cache = MemoryCache()
        
        # Set many values
        for i in range(100):
            cache.set(f"key_{i}", f"value_{i}")
            
        # Get all values
        for i in range(100):
            assert cache.get(f"key_{i}") == f"value_{i}"
            
        # Delete half
        for i in range(0, 100, 2):
            cache.delete(f"key_{i}")
            
        # Verify deletions
        for i in range(100):
            if i % 2 == 0:
                assert cache.get(f"key_{i}") is None
            else:
                assert cache.get(f"key_{i}") == f"value_{i}"
