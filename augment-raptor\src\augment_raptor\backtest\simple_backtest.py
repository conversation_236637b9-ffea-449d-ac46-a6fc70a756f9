"""
Simple backtest engine for trading strategies.

This module provides a basic backtesting framework that can simulate
trading strategies on historical data.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Any
from datetime import datetime
from ..strategies.base import Strategy
from ..utils.logger import Logger
from .metrics import PerformanceMetrics


class SimpleBacktest:
    """Simple backtesting engine for trading strategies."""

    def __init__(
        self, initial_capital: float = 100000.0, commission: float = 0.001, slippage: float = 0.001
    ):
        """Initialize backtest engine.

        Args:
            initial_capital: Starting capital amount
            commission: Commission rate per trade (0.001 = 0.1%)
            slippage: Slippage rate per trade (0.001 = 0.1%)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.logger = Logger(__name__)

        # Backtest state
        self.reset()

    def reset(self) -> None:
        """Reset backtest state."""
        self.capital = self.initial_capital
        self.position = 0.0  # Current position size
        self.entry_price = 0.0
        self.trades = []
        self.equity_curve = []
        self.current_trade: Optional[Dict[str, Any]] = None

    def run(self, strategy: Strategy, data: pd.DataFrame) -> Dict[str, Any]:
        """Run backtest on historical data.

        Args:
            strategy: Trading strategy to test
            data: Historical OHLCV data

        Returns:
            Dictionary containing backtest results
        """
        if not strategy.validate_data(data):
            return {"error": "Invalid data for backtesting"}

        try:
            self.reset()

            # Generate signals
            signals_df = strategy.generate_signals(data)

            if signals_df.empty:
                return {"error": "No signals generated"}

            # Process each day
            for i, (date, row) in enumerate(signals_df.iterrows()):
                self._process_day(date, row, i)

            # Close any open position at the end
            if self.position != 0:
                last_row = signals_df.iloc[-1]
                self._close_position(signals_df.index[-1], last_row["Close"], "End of backtest")

            # Calculate performance metrics
            metrics = self._calculate_metrics(signals_df)

            # Prepare results
            results = {
                "strategy": strategy.name,
                "parameters": strategy.get_parameters(),
                "start_date": signals_df.index[0].strftime("%Y-%m-%d"),
                "end_date": signals_df.index[-1].strftime("%Y-%m-%d"),
                "initial_capital": self.initial_capital,
                "final_capital": self.capital,
                "total_return": (self.capital - self.initial_capital) / self.initial_capital,
                "total_trades": len(self.trades),
                "metrics": metrics,
                "trades": self.trades,
                "equity_curve": self.equity_curve,
            }

            self.logger.info(
                f"Backtest completed: {len(self.trades)} trades, "
                f"{results['total_return']:.2%} return"
            )

            return results

        except Exception as e:
            self.logger.error(f"Error running backtest: {str(e)}")
            return {"error": str(e)}

    def _process_day(self, date: pd.Timestamp, row: pd.Series, day_index: int) -> None:
        """Process a single day of trading."""
        signal = row.get("signal", 0)
        price = row["Close"]

        # Record equity
        current_equity = self._calculate_current_equity(price)
        self.equity_curve.append(
            {"date": date, "equity": current_equity, "position": self.position, "price": price}
        )

        # Process signals
        if signal != 0 and self.position == 0:
            # New position
            self._open_position(date, row, signal)

        elif self.position != 0:
            # Check for exit conditions
            self._check_exit_conditions(date, row)

    def _open_position(self, date: pd.Timestamp, row: pd.Series, signal: int) -> None:
        """Open a new position."""
        entry_price = row.get("entry_price", row["Close"])
        stop_loss = row.get("stop_loss", np.nan)
        take_profit = row.get("take_profit", np.nan)

        # Apply slippage
        if signal > 0:  # Buy
            entry_price *= 1 + self.slippage
        else:  # Sell
            entry_price *= 1 - self.slippage

        # Calculate position size (use available capital)
        available_capital = self.capital * 0.95  # Keep 5% as buffer
        position_value = available_capital
        position_size = position_value / entry_price

        # Apply commission
        commission_cost = position_value * self.commission

        if signal > 0:  # Long position
            self.position = position_size
            self.capital -= position_value + commission_cost
        else:  # Short position
            self.position = -position_size
            self.capital += position_value - commission_cost

        self.entry_price = entry_price

        # Start new trade record
        self.current_trade = {
            "entry_date": date,
            "entry_price": entry_price,
            "position_size": abs(position_size),
            "direction": "long" if signal > 0 else "short",
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "commission": commission_cost,
        }

        self.logger.debug(
            f"Opened {self.current_trade['direction']} position: "
            f"{position_size:.2f} @ {entry_price:.2f}"
        )

    def _close_position(self, date: pd.Timestamp, exit_price: float, reason: str) -> None:
        """Close current position."""
        if self.position == 0 or self.current_trade is None:
            return

        # Apply slippage
        if self.position > 0:  # Closing long
            exit_price *= 1 - self.slippage
        else:  # Closing short
            exit_price *= 1 + self.slippage

        # Calculate P&L
        position_value = abs(self.position) * exit_price
        commission_cost = position_value * self.commission

        if self.position > 0:  # Long position
            pnl = (exit_price - self.entry_price) * self.position
            self.capital += position_value - commission_cost
        else:  # Short position
            pnl = (self.entry_price - exit_price) * abs(self.position)
            self.capital -= position_value + commission_cost

        # Complete trade record
        self.current_trade.update(
            {
                "exit_date": date,
                "exit_price": exit_price,
                "pnl": pnl,
                "return_pct": pnl / (abs(self.position) * self.entry_price),
                "holding_days": (date - self.current_trade["entry_date"]).days,
                "exit_reason": reason,
                "total_commission": self.current_trade["commission"] + commission_cost,
            }
        )

        self.trades.append(self.current_trade)

        self.logger.debug(f"Closed position: PnL = {pnl:.2f}, Reason = {reason}")

        # Reset position
        self.position = 0.0
        self.entry_price = 0.0
        self.current_trade = None

    def _check_exit_conditions(self, date: pd.Timestamp, row: pd.Series) -> None:
        """Check if position should be closed."""
        if self.position == 0 or self.current_trade is None:
            return

        current_price = row["Close"]
        high_price = row["High"]
        low_price = row["Low"]

        # Check stop loss
        stop_loss = self.current_trade.get("stop_loss")
        if not pd.isna(stop_loss):
            if self.position > 0 and low_price <= stop_loss:
                self._close_position(date, stop_loss, "Stop Loss")
                return
            elif self.position < 0 and high_price >= stop_loss:
                self._close_position(date, stop_loss, "Stop Loss")
                return

        # Check take profit
        take_profit = self.current_trade.get("take_profit")
        if not pd.isna(take_profit):
            if self.position > 0 and high_price >= take_profit:
                self._close_position(date, take_profit, "Take Profit")
                return
            elif self.position < 0 and low_price <= take_profit:
                self._close_position(date, take_profit, "Take Profit")
                return

        # Check for opposite signal
        signal = row.get("signal", 0)
        if signal != 0:
            if (self.position > 0 and signal < 0) or (self.position < 0 and signal > 0):
                self._close_position(date, current_price, "Opposite Signal")

    def _calculate_current_equity(self, current_price: float) -> float:
        """Calculate current equity including unrealized P&L."""
        if self.position == 0:
            return self.capital

        # Calculate unrealized P&L
        if self.position > 0:  # Long position
            unrealized_pnl = (current_price - self.entry_price) * self.position
        else:  # Short position
            unrealized_pnl = (self.entry_price - current_price) * abs(self.position)

        return self.capital + abs(self.position) * self.entry_price + unrealized_pnl

    def _calculate_metrics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate performance metrics."""
        if not self.trades:
            return {}

        metrics_calculator = PerformanceMetrics(
            self.trades, self.equity_curve, self.initial_capital
        )

        return metrics_calculator.calculate_all_metrics()
