# Thiết kế Database cho Dự án Augment Volume Skipe Breakout

## Ngày: [2025-06-30]

## 1. Tổng quan

Tài liệu này trình bày phân tích và đề xuất thiết kế database cho dự án Augment Volume Skipe Breakout. Database là thành phần quan trọng không kém phần refactoring code, đóng vai trò nền tảng cho việc lưu trữ, truy xuất và phân tích dữ liệu trong hệ thống trading algorithm.

## 2. Phân tích yêu cầu dữ liệu

### 2.1. Loại dữ liệu cần lưu trữ

1. **D<PERSON> liệu thị trường lịch sử**
   - Gi<PERSON> OHLCV (Open, High, Low, Close, Volume) theo ngày/giờ/phút
   - Dữ liệu chỉ số thị trường (VN-Index, HNX-Index, v.v.)
   - <PERSON><PERSON> liệu ngành và phân ngành

2. **<PERSON><PERSON> liệu công ty**
   - Thông tin cơ bản (mã, tên, ngành, v<PERSON>n hóa, v.v.)
   - Dữ liệu tài chính (EPS, P/E, ROE, v.v.)
   - Sự kiện doanh nghiệp (chia cổ tức, phát hành, v.v.)

3. **Dữ liệu phân tích kỹ thuật**
   - Các chỉ báo kỹ thuật đã tính toán (MA, RSI, MACD, v.v.)
   - Mẫu hình kỹ thuật đã phát hiện (tích lũy, breakout, v.v.)
   - Điểm số và xếp hạng theo các chiến lược

4. **Dữ liệu giao dịch**
   - Lịch sử giao dịch (backtest và paper trading)
   - Danh mục đầu tư hiện tại
   - Hiệu suất giao dịch theo thời gian

### 2.2. Đặc điểm và yêu cầu

1. **Khối lượng dữ liệu**
   - Dữ liệu OHLCV: ~300 mã cổ phiếu × 252 phiên/năm × 10 năm ≈ 756,000 bản ghi
   - Dữ liệu intraday (nếu có): Tăng theo cấp số nhân (hàng triệu bản ghi)
   - Dữ liệu tài chính: ~300 mã × 4 quý/năm × 10 năm ≈ 12,000 bản ghi

2. **Tần suất truy cập**
   - Đọc nhiều hơn ghi (read-heavy workload)
   - Cập nhật dữ liệu thị trường: hàng ngày hoặc theo thời gian thực
   - Truy vấn phân tích: thường xuyên, đặc biệt khi chạy backtest và screening

3. **Yêu cầu hiệu năng**
   - Truy vấn nhanh cho dữ liệu lịch sử (để backtest hiệu quả)
   - Xử lý hiệu quả các truy vấn phân tích phức tạp
   - Khả năng xử lý dữ liệu thời gian thực (nếu cần)

4. **Yêu cầu tính nhất quán và độ tin cậy**
   - Đảm bảo tính toàn vẹn dữ liệu tài chính
   - Xử lý hiệu quả các điều chỉnh giá (splits, dividends)
   - Khả năng phục hồi dữ liệu khi có sự cố

## 3. Lựa chọn công nghệ database

### 3.1. Các lựa chọn và phân tích

#### 3.1.1. DuckDB

**Ưu điểm:**
- Thiết kế tối ưu cho phân tích (OLAP) với hiệu suất cao
- Nhẹ, không cần server riêng, dễ nhúng vào ứng dụng
- Hỗ trợ tốt cho dữ liệu dạng cột (columnar storage)
- Tích hợp tốt với Pandas và các công cụ phân tích dữ liệu
- Hỗ trợ Parquet format nên tiết kiệm không gian lưu trữ
- Hiệu suất truy vấn vượt trội cho dữ liệu phân tích

**Nhược điểm:**
- Không phù hợp cho nhiều người dùng đồng thời
- Khả năng mở rộng hạn chế so với các hệ thống database phân tán
- Công nghệ tương đối mới, cộng đồng nhỏ hơn

#### 3.1.2. SQLite

**Ưu điểm:**
- Nhẹ, không cần server, dễ sử dụng
- Độ tin cậy cao, được sử dụng rộng rãi
- Không cần cấu hình phức tạp
- Hỗ trợ đầy đủ SQL standard

**Nhược điểm:**
- Hiệu suất hạn chế với dữ liệu lớn
- Không tối ưu cho truy vấn phân tích phức tạp
- Không hỗ trợ truy cập đồng thời tốt
- Không có tính năng nâng cao như partitioning, indexing phức tạp

#### 3.1.3. PostgreSQL

**Ưu điểm:**
- Hệ thống database toàn diện, enterprise-grade
- Hỗ trợ đầy đủ ACID, đảm bảo tính nhất quán dữ liệu
- Khả năng mở rộng tốt, hỗ trợ nhiều người dùng đồng thời
- Hỗ trợ nhiều tính năng nâng cao (JSON, time-series, full-text search)
- Cộng đồng lớn, nhiều extension hữu ích (TimescaleDB cho time-series data)

**Nhược điểm:**
- Yêu cầu cài đặt và cấu hình server riêng
- Tốn tài nguyên hệ thống hơn các giải pháp nhẹ
- Cần nhiều công sức để tối ưu hóa hiệu suất

#### 3.1.4. MongoDB (NoSQL)

**Ưu điểm:**
- Linh hoạt với schema động
- Hiệu suất tốt cho các thao tác đọc/ghi đơn giản
- Dễ dàng mở rộng theo chiều ngang
- Hỗ trợ tốt cho dữ liệu dạng document

**Nhược điểm:**
- Không tối ưu cho truy vấn phân tích phức tạp
- Không đảm bảo ACID transactions như các database quan hệ
- Tốn nhiều không gian lưu trữ hơn do lưu trữ dạng document

### 3.2. Đề xuất giải pháp

Dựa trên phân tích yêu cầu và đặc điểm của dự án, tôi đề xuất **giải pháp kết hợp**:

#### 3.2.1. Giải pháp chính: DuckDB + Parquet

**Lý do chọn:**
- Dự án có đặc tính read-heavy và cần hiệu suất cao cho phân tích
- Khối lượng dữ liệu vừa phải, phù hợp với khả năng của DuckDB
- Tích hợp tốt với Python ecosystem (Pandas, NumPy)
- Format Parquet tiết kiệm không gian và tối ưu cho truy vấn phân tích
- Không cần cài đặt server database riêng, giảm độ phức tạp hệ thống

**Cách triển khai:**
- Sử dụng DuckDB làm database chính cho dữ liệu lịch sử và phân tích
- Lưu trữ dữ liệu dạng Parquet files, tổ chức theo cấu trúc thư mục hợp lý
- Sử dụng memory-mapped mode cho truy vấn nhanh
- Tạo các view và materialized views cho các truy vấn phổ biến

#### 3.2.2. Giải pháp bổ sung: SQLite

**Vai trò:**
- Lưu trữ metadata, cấu hình hệ thống, và dữ liệu giao dịch
- Đảm bảo ACID cho các thao tác quan trọng
- Backup và restore dễ dàng

**Cách triển khai:**
- Sử dụng SQLite cho dữ liệu cấu hình, user settings, và transaction logs
- Tạo cơ chế đồng bộ giữa SQLite và DuckDB khi cần thiết

#### 3.2.3. Chiến lược mở rộng trong tương lai

Khi hệ thống phát triển và yêu cầu mở rộng:
- **Giai đoạn 1**: Tối ưu hóa DuckDB (partitioning, indexing, query optimization)
- **Giai đoạn 2**: Chuyển sang PostgreSQL + TimescaleDB cho time-series data
- **Giai đoạn 3**: Xem xét giải pháp phân tán như ClickHouse hoặc Dask

## 4. Thiết kế kiến trúc dữ liệu

### 4.1. Schema database

#### 4.1.1. DuckDB/Parquet Schema

**1. Market Data Schema**

```sql
-- Bảng dữ liệu OHLCV hàng ngày
CREATE TABLE daily_prices (
    symbol VARCHAR NOT NULL,
    date DATE NOT NULL,
    open DECIMAL(10,2),
    high DECIMAL(10,2),
    low DECIMAL(10,2),
    close DECIMAL(10,2),
    adjusted_close DECIMAL(10,2),
    volume BIGINT,
    PRIMARY KEY (symbol, date)
);

-- Bảng dữ liệu chỉ số thị trường
CREATE TABLE market_indices (
    index_code VARCHAR NOT NULL,
    date DATE NOT NULL,
    value DECIMAL(10,2),
    change_percent DECIMAL(5,2),
    volume BIGINT,
    PRIMARY KEY (index_code, date)
);

-- Bảng phân ngành
CREATE TABLE sectors (
    sector_id INTEGER PRIMARY KEY,
    sector_name VARCHAR NOT NULL,
    parent_sector_id INTEGER,
    description TEXT
);

-- Bảng phân loại cổ phiếu theo ngành
CREATE TABLE stock_sectors (
    symbol VARCHAR NOT NULL,
    sector_id INTEGER NOT NULL,
    weight DECIMAL(5,2),
    PRIMARY KEY (symbol, sector_id)
);
```

**2. Company Data Schema**

```sql
-- Bảng thông tin công ty
CREATE TABLE companies (
    symbol VARCHAR PRIMARY KEY,
    company_name VARCHAR NOT NULL,
    exchange VARCHAR,
    listing_date DATE,
    website VARCHAR,
    business_summary TEXT,
    market_cap DECIMAL(20,2),
    outstanding_shares BIGINT,
    is_active BOOLEAN DEFAULT TRUE
);

-- Bảng dữ liệu tài chính theo quý
CREATE TABLE financial_data (
    symbol VARCHAR NOT NULL,
    period_end_date DATE NOT NULL,
    report_type VARCHAR NOT NULL, -- quarterly, annual
    revenue DECIMAL(20,2),
    net_income DECIMAL(20,2),
    eps DECIMAL(10,2),
    pe_ratio DECIMAL(10,2),
    roe DECIMAL(10,2),
    roa DECIMAL(10,2),
    debt_to_equity DECIMAL(10,2),
    book_value DECIMAL(10,2),
    PRIMARY KEY (symbol, period_end_date, report_type)
);

-- Bảng sự kiện doanh nghiệp
CREATE TABLE corporate_actions (
    id INTEGER PRIMARY KEY,
    symbol VARCHAR NOT NULL,
    action_date DATE NOT NULL,
    ex_date DATE,
    action_type VARCHAR NOT NULL, -- dividend, split, rights, etc.
    value DECIMAL(10,4), -- dividend amount, split ratio, etc.
    description TEXT
);
```

**3. Technical Analysis Schema**

```sql
-- Bảng chỉ báo kỹ thuật
CREATE TABLE technical_indicators (
    symbol VARCHAR NOT NULL,
    date DATE NOT NULL,
    indicator_type VARCHAR NOT NULL, -- MA, RSI, MACD, etc.
    timeframe VARCHAR NOT NULL, -- daily, weekly, etc.
    parameters VARCHAR, -- JSON string of parameters
    value DECIMAL(15,5),
    PRIMARY KEY (symbol, date, indicator_type, timeframe, parameters)
);

-- Bảng mẫu hình kỹ thuật
CREATE TABLE technical_patterns (
    id INTEGER PRIMARY KEY,
    symbol VARCHAR NOT NULL,
    pattern_type VARCHAR NOT NULL, -- consolidation, breakout, etc.
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    confidence DECIMAL(5,2), -- 0-100%
    parameters VARCHAR, -- JSON string of parameters
    description TEXT
);

-- Bảng điểm số và xếp hạng
CREATE TABLE stock_scores (
    symbol VARCHAR NOT NULL,
    date DATE NOT NULL,
    strategy VARCHAR NOT NULL, -- volume_breakout, etc.
    score DECIMAL(5,2),
    rank INTEGER,
    details VARCHAR, -- JSON string of score details
    PRIMARY KEY (symbol, date, strategy)
);
```

**4. Trading Data Schema**

```sql
-- Bảng giao dịch (backtest và paper trading)
CREATE TABLE trades (
    trade_id INTEGER PRIMARY KEY,
    strategy VARCHAR NOT NULL,
    symbol VARCHAR NOT NULL,
    entry_date TIMESTAMP NOT NULL,
    entry_price DECIMAL(10,2) NOT NULL,
    entry_type VARCHAR NOT NULL, -- buy, sell
    quantity INTEGER NOT NULL,
    exit_date TIMESTAMP,
    exit_price DECIMAL(10,2),
    pnl DECIMAL(10,2),
    pnl_percent DECIMAL(5,2),
    trade_status VARCHAR NOT NULL, -- open, closed
    notes TEXT
);

-- Bảng danh mục đầu tư
CREATE TABLE portfolio (
    portfolio_id INTEGER NOT NULL,
    symbol VARCHAR NOT NULL,
    quantity INTEGER NOT NULL,
    average_price DECIMAL(10,2) NOT NULL,
    current_price DECIMAL(10,2),
    market_value DECIMAL(15,2),
    weight DECIMAL(5,2),
    unrealized_pnl DECIMAL(10,2),
    unrealized_pnl_percent DECIMAL(5,2),
    last_update TIMESTAMP,
    PRIMARY KEY (portfolio_id, symbol)
);

-- Bảng hiệu suất giao dịch
CREATE TABLE performance_metrics (
    strategy VARCHAR NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_trades INTEGER,
    winning_trades INTEGER,
    losing_trades INTEGER,
    win_rate DECIMAL(5,2),
    profit_factor DECIMAL(5,2),
    sharpe_ratio DECIMAL(5,2),
    max_drawdown DECIMAL(5,2),
    cagr DECIMAL(5,2),
    PRIMARY KEY (strategy, period_start, period_end)
);
```

#### 4.1.2. SQLite Schema

```sql
-- Bảng cấu hình hệ thống
CREATE TABLE system_config (
    config_key VARCHAR PRIMARY KEY,
    config_value VARCHAR NOT NULL,
    data_type VARCHAR NOT NULL, -- string, int, float, json, etc.
    description TEXT,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng cấu hình chiến lược
CREATE TABLE strategy_config (
    strategy_id VARCHAR NOT NULL,
    config_key VARCHAR NOT NULL,
    config_value VARCHAR NOT NULL,
    data_type VARCHAR NOT NULL,
    description TEXT,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (strategy_id, config_key)
);

-- Bảng metadata cache
CREATE TABLE data_cache_metadata (
    cache_key VARCHAR PRIMARY KEY,
    data_type VARCHAR NOT NULL,
    source VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP,
    file_path VARCHAR,
    file_size INTEGER,
    record_count INTEGER,
    status VARCHAR NOT NULL -- valid, expired, invalid
);

-- Bảng log hệ thống
CREATE TABLE system_logs (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    log_level VARCHAR NOT NULL, -- info, warning, error, etc.
    component VARCHAR NOT NULL,
    message TEXT,
    details TEXT
);
```

### 4.2. Quy tắc normalization/denormalization

#### 4.2.1. Nguyên tắc chung

1. **Normalization cho dữ liệu giao dịch và cấu hình**
   - Áp dụng 3NF (Third Normal Form) cho dữ liệu giao dịch, cấu hình và metadata
   - Đảm bảo tính toàn vẹn dữ liệu và tránh redundancy

2. **Denormalization cho dữ liệu phân tích**
   - Áp dụng denormalization có chọn lọc cho dữ liệu thị trường và phân tích
   - Tối ưu hóa hiệu suất truy vấn cho các phân tích phức tạp

#### 4.2.2. Chiến lược cụ thể

1. **Star Schema cho dữ liệu phân tích**
   - Bảng fact: `daily_prices`, `technical_indicators`
   - Bảng dimension: `companies`, `sectors`, `time_dimension`

2. **Materialized Views cho truy vấn phổ biến**
   - Tạo materialized views cho các truy vấn phức tạp và thường xuyên
   - Ví dụ: view cho top stocks theo volume breakout score

3. **Partitioning dữ liệu theo thời gian**
   - Chia dữ liệu thành các partition theo năm/quý
   - Tối ưu hiệu suất truy vấn cho dữ liệu lịch sử

### 4.3. Chiến lược quản lý dữ liệu lịch sử và thời gian thực

#### 4.3.1. Dữ liệu lịch sử

1. **Tổ chức lưu trữ**
   - Lưu trữ dữ liệu dạng Parquet files, tổ chức theo cấu trúc thư mục phân cấp:
     ```
     data/
     ├── market/
     │   ├── daily/
     │   │   ├── 2020/
     │   │   ├── 2021/
     │   │   └── ...
     │   └── indices/
     ├── company/
     │   ├── info/
     │   └── financials/
     └── technical/
         ├── indicators/
         └── patterns/
     ```

2. **Chiến lược nén và lưu trữ**
   - Sử dụng Parquet với nén Snappy hoặc Zstd
   - Tối ưu hóa column chunking và row grouping

3. **Retention policy**
   - Dữ liệu chi tiết: giữ 5-10 năm
   - Dữ liệu tổng hợp: giữ vĩnh viễn
   - Dữ liệu ít sử dụng: chuyển sang cold storage

#### 4.3.2. Dữ liệu thời gian thực

1. **Buffer layer**
   - Sử dụng in-memory buffer cho dữ liệu thời gian thực
   - Định kỳ flush vào storage chính

2. **Incremental updates**
   - Thiết kế cơ chế cập nhật gia tăng cho dữ liệu mới
   - Tối ưu hóa quá trình merge dữ liệu mới vào dataset hiện có

3. **Change data capture**
   - Theo dõi và ghi lại các thay đổi dữ liệu
   - Hỗ trợ rollback và audit trail

### 4.4. Tối ưu hóa cho truy vấn phân tích

#### 4.4.1. Indexing strategy

1. **Primary indexes**
   - Composite index trên (symbol, date) cho các bảng chính
   - Đảm bảo truy vấn nhanh theo mã cổ phiếu và khoảng thời gian

2. **Secondary indexes**
   - Index trên các cột thường xuyên được sử dụng trong WHERE clause
   - Ví dụ: index trên `score` trong bảng `stock_scores`

3. **Bitmap indexes**
   - Áp dụng cho các cột có tính chọn lọc thấp (low cardinality)
   - Ví dụ: index trên `sector_id`, `exchange`

#### 4.4.2. Materialized aggregations

1. **Pre-calculated metrics**
   - Tính toán trước các chỉ số thường xuyên sử dụng
   - Ví dụ: moving averages, volatility, correlation matrices

2. **Time-based aggregations**
   - Tổng hợp dữ liệu theo các khung thời gian khác nhau
   - Ví dụ: daily → weekly → monthly → quarterly

#### 4.4.3. Query optimization

1. **Query templates**
   - Tạo các template cho các truy vấn phổ biến
   - Tối ưu hóa execution plan

2. **Parallel processing**
   - Tận dụng khả năng xử lý song song của DuckDB
   - Phân chia các truy vấn lớn thành các phần nhỏ hơn

## 5. Tích hợp với kiến trúc code mới

### 5.1. Kết nối Data Layer với database

#### 5.1.1. Kiến trúc tổng thể

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Data Sources   │─────▶│   Data Layer    │─────▶│   Core Engine   │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
                                 │
                                 │
                         ┌───────▼───────┐
                         │               │
                         │   Database    │
                         │               │
                         └───────────────┘
                                 │
                         ┌───────▼───────┐
                         │               │
                         │ File Storage  │
                         │               │
                         └───────────────┘
```

#### 5.1.2. Interface design

```python
# src/data_layer/storage/base.py
from abc import ABC, abstractmethod
import pandas as pd
from typing import List, Dict, Any, Optional

class DataStorage(ABC):
    """Abstract base class for data storage implementations."""
    
    @abstractmethod
    def save(self, symbol: str, data: pd.DataFrame, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Save data for a symbol to storage."""
        pass
    
    @abstractmethod
    def load(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Load data for a symbol from storage."""
        pass
    
    @abstractmethod
    def exists(self, symbol: str) -> bool:
        """Check if data exists for a symbol."""
        pass
    
    @abstractmethod
    def delete(self, symbol: str) -> bool:
        """Delete data for a symbol."""
        pass
    
    @abstractmethod
    def list_symbols(self) -> List[str]:
        """List all available symbols in storage."""
        pass
    
    @abstractmethod
    def get_metadata(self, symbol: str) -> Dict[str, Any]:
        """Get metadata for a symbol."""
        pass
    
    @abstractmethod
    def update_metadata(self, symbol: str, metadata: Dict[str, Any]) -> bool:
        """Update metadata for a symbol."""
        pass
```

#### 5.1.3. DuckDB implementation

```python
# src/data_layer/storage/duckdb_storage.py
import os
import json
import pandas as pd
import duckdb
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from src.data_layer.storage.base import DataStorage
from src.utils.config import ConfigManager

class DuckDBStorage(DataStorage):
    """Data storage implementation using DuckDB and Parquet."""
    
    def __init__(self, config: Optional[ConfigManager] = None):
        """Initialize DuckDBStorage.
        
        Args:
            config: ConfigManager instance
        """
        self.config = config or ConfigManager()
        self.cache_dir = self.config.get('data.cache_dir', './cache')
        self.metadata_dir = os.path.join(self.cache_dir, 'metadata')
        
        # Tạo thư mục cache nếu chưa tồn tại
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        # Kết nối DuckDB
        self.conn = duckdb.connect(database=':memory:')
        
    def _get_parquet_path(self, symbol: str) -> str:
        """Get path to parquet file for a symbol."""
        return os.path.join(self.cache_dir, f"{symbol}.parquet")
    
    def _get_metadata_path(self, symbol: str) -> str:
        """Get path to metadata file for a symbol."""
        return os.path.join(self.metadata_dir, f"{symbol}.json")
    
    def save(self, symbol: str, data: pd.DataFrame, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Save data for a symbol to storage.
        
        Args:
            symbol: Stock symbol
            data: DataFrame with stock data
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure data is sorted by date
            if 'date' in data.columns:
                data = data.sort_values('date')
            
            # Save to parquet
            parquet_path = self._get_parquet_path(symbol)
            data.to_parquet(parquet_path, index=False)
            
            # Update metadata
            current_metadata = self.get_metadata(symbol)
            new_metadata = {
                'symbol': symbol,
                'last_updated': datetime.now().isoformat(),
                'rows': len(data),
                'columns': list(data.columns),
                'start_date': data['date'].min().isoformat() if 'date' in data.columns else None,
                'end_date': data['date'].max().isoformat() if 'date' in data.columns else None,
                'file_size': os.path.getsize(parquet_path)
            }
            
            # Merge with provided metadata
            if metadata:
                new_metadata.update(metadata)
            
            # Merge with existing metadata
            current_metadata.update(new_metadata)
            
            # Save metadata
            with open(self._get_metadata_path(symbol), 'w') as f:
                json.dump(current_metadata, f, indent=2)
                
            return True
        except Exception as e:
            print(f"Error saving data for {symbol}: {str(e)}")
            return False
    
    def load(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Load data for a symbol from storage.
        
        Args:
            symbol: Stock symbol
            start_date: Optional start date (ISO format)
            end_date: Optional end date (ISO format)
            
        Returns:
            DataFrame with stock data or None if not found
        """
        try:
            parquet_path = self._get_parquet_path(symbol)
            
            if not os.path.exists(parquet_path):
                return None
            
            # Construct query
            query = f"SELECT * FROM parquet_scan('{parquet_path}')"
            
            # Add date filters if provided
            if start_date or end_date:
                query += " WHERE "
                conditions = []
                
                if start_date:
                    conditions.append(f"date >= '{start_date}'")
                
                if end_date:
                    conditions.append(f"date <= '{end_date}'")
                
                query += " AND ".join(conditions)
            
            # Execute query
            result = self.conn.execute(query).fetchdf()
            return result
        except Exception as e:
            print(f"Error loading data for {symbol}: {str(e)}")
            return None
    
    def exists(self, symbol: str) -> bool:
        """Check if data exists for a symbol."""
        return os.path.exists(self._get_parquet_path(symbol))
    
    def delete(self, symbol: str) -> bool:
        """Delete data for a symbol."""
        try:
            parquet_path = self._get_parquet_path(symbol)
            metadata_path =
</augment_code_snippet>