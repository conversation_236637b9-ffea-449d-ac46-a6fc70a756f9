[flake8]
max-line-length = 100
extend-ignore = 
    E203,  # whitespace before ':'
    W503,  # line break before binary operator
    E402,  # module level import not at top of file (for tests)
    F401,  # imported but unused (common in __init__.py)
    F403,  # star import used
    W293,  # blank line contains whitespace
    W291,  # trailing whitespace
    E712,  # comparison to True/False
    F841,  # local variable assigned but never used
    F541,  # f-string is missing placeholders
    E128,  # continuation line under-indented
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info
per-file-ignores =
    __init__.py:F401,F403
    tests/*:E402,F401,E712
    */test_*.py:E402,F401,E712
