"""
Volume Breakout Strategy Implementation

This strategy identifies trading opportunities based on volume spikes
combined with price breakouts, indicating strong momentum.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from .base import Strategy


class VolumeBreakoutStrategy(Strategy):
    """Volume Breakout trading strategy.

    This strategy looks for:
    1. Volume spike above average (volume_threshold)
    2. Price breakout above resistance or below support
    3. Confirmation through technical indicators
    """

    def __init__(self, parameters: Dict[str, Any] = None):
        """Initialize Volume Breakout strategy.

        Args:
            parameters: Strategy parameters including:
                - volume_threshold: Volume ratio threshold (default: 2.0)
                - price_threshold: Price change threshold (default: 0.02)
                - volume_period: Volume moving average period (default: 20)
                - confirmation_period: Confirmation period (default: 3)
                - stop_loss_atr: Stop loss in ATR units (default: 2.0)
                - take_profit_atr: Take profit in ATR units (default: 4.0)
        """
        default_params = {
            "volume_threshold": 2.0,
            "price_threshold": 0.02,
            "volume_period": 20,
            "confirmation_period": 3,
            "stop_loss_atr": 2.0,
            "take_profit_atr": 4.0,
            "min_volume": 100000,  # Minimum volume filter
            "trend_filter": True,  # Use trend filter
        }

        if parameters:
            default_params.update(parameters)

        super().__init__("Volume Breakout", default_params)

    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data for volume breakout opportunities."""
        if not self.validate_data(data):
            return {"error": "Invalid data provided"}

        try:
            # Generate signals
            signals_df = self.generate_signals(data)

            # Count signals
            buy_signals = (signals_df["signal"] == 1).sum()
            sell_signals = (signals_df["signal"] == -1).sum()

            # Calculate signal quality metrics
            signal_strength_avg = signals_df[signals_df["signal"] != 0]["signal_strength"].mean()

            # Recent signals (last 10 periods)
            recent_signals = signals_df.tail(10)
            recent_buy = (recent_signals["signal"] == 1).sum()
            recent_sell = (recent_signals["signal"] == -1).sum()

            # Current market state
            current_data = data.tail(1).iloc[0]
            current_volume_ratio = current_data.get("Volume_Ratio_20", 0)
            current_price_change = current_data.get("Price_Change_Pct", 0)

            analysis_result = {
                "strategy": self.name,
                "total_signals": buy_signals + sell_signals,
                "buy_signals": int(buy_signals),
                "sell_signals": int(sell_signals),
                "signal_strength_avg": float(signal_strength_avg)
                if not pd.isna(signal_strength_avg)
                else 0.0,
                "recent_buy_signals": int(recent_buy),
                "recent_sell_signals": int(recent_sell),
                "current_volume_ratio": float(current_volume_ratio),
                "current_price_change": float(current_price_change),
                "parameters": self.parameters,
                "data_points": len(data),
                "last_signal_date": signals_df[signals_df["signal"] != 0]
                .index[-1]
                .strftime("%Y-%m-%d")
                if len(signals_df[signals_df["signal"] != 0]) > 0
                else None,
            }

            return analysis_result

        except Exception as e:
            self.logger.error(f"Error in analysis: {str(e)}")
            return {"error": str(e)}

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate Volume Breakout trading signals."""
        if not self.validate_data(data):
            return pd.DataFrame()

        # Make a copy to avoid modifying original data
        df = data.copy()

        # Ensure required indicators are calculated
        df = self._calculate_indicators(df)

        # Initialize signal columns
        df["signal"] = 0
        df["signal_strength"] = 0.0
        df["entry_price"] = np.nan
        df["stop_loss"] = np.nan
        df["take_profit"] = np.nan

        # Volume breakout condition
        volume_condition = (df["Volume_Ratio_20"] >= self.parameters["volume_threshold"]) & (
            df["Volume"] >= self.parameters["min_volume"]
        )

        # Price breakout conditions
        price_up_condition = df["Price_Change_Pct"] >= (self.parameters["price_threshold"] * 100)
        price_down_condition = df["Price_Change_Pct"] <= -(self.parameters["price_threshold"] * 100)

        # Trend filter (optional)
        if self.parameters["trend_filter"]:
            uptrend = df["Close"] > df["SMA_20"]
            downtrend = df["Close"] < df["SMA_20"]
        else:
            uptrend = True
            downtrend = True

        # Buy signals (volume breakout + price up + uptrend)
        buy_condition = volume_condition & price_up_condition & uptrend

        # Sell signals (volume breakout + price down + downtrend)
        sell_condition = volume_condition & price_down_condition & downtrend

        # Apply signals
        df.loc[buy_condition, "signal"] = 1
        df.loc[sell_condition, "signal"] = -1

        # Calculate signal strength
        df.loc[buy_condition, "signal_strength"] = np.minimum(
            df.loc[buy_condition, "Volume_Ratio_20"] / self.parameters["volume_threshold"],
            df.loc[buy_condition, "Price_Change_Pct"] / (self.parameters["price_threshold"] * 100),
        )

        df.loc[sell_condition, "signal_strength"] = np.minimum(
            df.loc[sell_condition, "Volume_Ratio_20"] / self.parameters["volume_threshold"],
            abs(df.loc[sell_condition, "Price_Change_Pct"])
            / (self.parameters["price_threshold"] * 100),
        )

        # Set entry prices and risk management levels
        df = self._set_risk_management_levels(df)

        return df

    def _calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate required technical indicators."""
        df = data.copy()

        # Volume indicators
        period = self.parameters["volume_period"]
        df[f"Volume_SMA_{period}"] = df["Volume"].rolling(window=period).mean()
        df[f"Volume_Ratio_{period}"] = df["Volume"] / df[f"Volume_SMA_{period}"]

        # Price indicators
        df["Price_Change_Pct"] = df["Close"].pct_change() * 100
        df["SMA_20"] = df["Close"].rolling(window=20).mean()

        # ATR for risk management
        df["True_Range"] = np.maximum(
            df["High"] - df["Low"],
            np.maximum(
                abs(df["High"] - df["Close"].shift(1)), abs(df["Low"] - df["Close"].shift(1))
            ),
        )
        df["ATR_14"] = df["True_Range"].rolling(window=14).mean()

        return df

    def _set_risk_management_levels(self, data: pd.DataFrame) -> pd.DataFrame:
        """Set entry, stop loss, and take profit levels."""
        df = data.copy()

        # For buy signals
        buy_mask = df["signal"] == 1
        if buy_mask.any():
            df.loc[buy_mask, "entry_price"] = df.loc[buy_mask, "Close"]
            df.loc[buy_mask, "stop_loss"] = (
                df.loc[buy_mask, "Close"]
                - df.loc[buy_mask, "ATR_14"] * self.parameters["stop_loss_atr"]
            )
            df.loc[buy_mask, "take_profit"] = (
                df.loc[buy_mask, "Close"]
                + df.loc[buy_mask, "ATR_14"] * self.parameters["take_profit_atr"]
            )

        # For sell signals
        sell_mask = df["signal"] == -1
        if sell_mask.any():
            df.loc[sell_mask, "entry_price"] = df.loc[sell_mask, "Close"]
            df.loc[sell_mask, "stop_loss"] = (
                df.loc[sell_mask, "Close"]
                + df.loc[sell_mask, "ATR_14"] * self.parameters["stop_loss_atr"]
            )
            df.loc[sell_mask, "take_profit"] = (
                df.loc[sell_mask, "Close"]
                - df.loc[sell_mask, "ATR_14"] * self.parameters["take_profit_atr"]
            )

        return df

    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return self.parameters.copy()

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """Set strategy parameters."""
        self.parameters.update(parameters)
        self.logger.info(f"Updated parameters: {parameters}")

    def get_minimum_data_length(self) -> int:
        """Get minimum data length required."""
        return max(50, self.parameters["volume_period"] + 20)

    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators."""
        return [
            f'Volume_SMA_{self.parameters["volume_period"]}',
            f'Volume_Ratio_{self.parameters["volume_period"]}',
            "Price_Change_Pct",
            "SMA_20",
            "ATR_14",
        ]
