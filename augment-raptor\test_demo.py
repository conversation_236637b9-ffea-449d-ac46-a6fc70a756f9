import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from augment_raptor.strategies.volume_breakout import VolumeBreakoutStrategy
import pandas as pd
import numpy as np

# Test basic functionality
strategy = VolumeBreakoutStrategy()
print(f"Strategy created: {strategy.name}")
print(f"Parameters: {strategy.parameters}")

# Create test data
np.random.seed(42)
data = pd.DataFrame({
    'Open': 100 + np.random.randn(20) * 2,
    'High': 102 + np.random.randn(20) * 2,
    'Low': 98 + np.random.randn(20) * 2,
    'Close': 100 + np.random.randn(20) * 2,
    'Volume': 1000000 + np.random.randn(20) * 100000
})

# Fix OHLC relationships
data['High'] = np.maximum(data['High'], np.maximum(data['Open'], data['Close']))
data['Low'] = np.minimum(data['Low'], np.minimum(data['Open'], data['Close']))

print(f"Data shape: {data.shape}")

# Test analysis
result = strategy.analyze(data)
print("Analysis completed successfully!")
print(f"Signals: {result['signals'].shape}")
print(f"Metrics: {list(result['metrics'].keys())}")
